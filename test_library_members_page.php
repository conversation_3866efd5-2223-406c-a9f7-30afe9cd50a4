<?php
/**
 * Simple test to check if library members page loads
 * Place this in your web root and access via browser
 * Remove after testing
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Library Members Page Test</h2>";

// Test basic PHP functionality
echo "<h3>1. PHP Test</h3>";
echo "✅ PHP is working<br>";
echo "PHP Version: " . phpversion() . "<br>";

// Test if we can include CodeIgniter
echo "<h3>2. CodeIgniter Bootstrap Test</h3>";
try {
    // Include the main index file to bootstrap CodeIgniter
    ob_start();
    $_SERVER['REQUEST_URI'] = '/admin/member';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    // Capture any output
    include('index.php');
    $output = ob_get_clean();
    
    if (strpos($output, 'Fatal error') !== false || strpos($output, 'Parse error') !== false) {
        echo "❌ CodeIgniter bootstrap failed<br>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    } else {
        echo "✅ CodeIgniter bootstrap successful<br>";
        if (strlen($output) > 0) {
            echo "Page output length: " . strlen($output) . " characters<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Exception during bootstrap: " . $e->getMessage() . "<br>";
}

echo "<h3>3. Direct Model Test</h3>";
try {
    // Get CI instance if available
    if (function_exists('get_instance')) {
        $CI =& get_instance();
        
        // Test database connection
        $db_test = $CI->db->query("SELECT 1 as test")->row();
        echo "✅ Database connection working<br>";
        
        // Test loading the model
        $CI->load->model('librarymember_model');
        echo "✅ Library member model loaded<br>";
        
        // Test the get method
        $members = $CI->librarymember_model->get();
        echo "✅ Library members retrieved: " . count($members) . " members<br>";
        
        if (!empty($members)) {
            echo "Sample member data:<br>";
            echo "<pre>";
            print_r(array_slice($members, 0, 1));
            echo "</pre>";
        }
        
    } else {
        echo "❌ CodeIgniter not properly loaded<br>";
    }
} catch (Exception $e) {
    echo "❌ Model test failed: " . $e->getMessage() . "<br>";
}

echo "<h3>4. Recommendations</h3>";
echo "<ul>";
echo "<li>If you see any fatal errors above, check the error logs</li>";
echo "<li>If database connection fails, check database configuration</li>";
echo "<li>If model loading fails, check file permissions</li>";
echo "<li>Try accessing the actual page: <a href='/admin/member'>/admin/member</a></li>";
echo "</ul>";

echo "<p><strong>Remove this test file after debugging!</strong></p>";
?>
