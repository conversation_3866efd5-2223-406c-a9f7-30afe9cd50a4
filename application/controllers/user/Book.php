<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Book extends Student_Controller {

    function __construct() {
        parent::__construct();
    }

    public function index() {
        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'book/index');
        $data['title'] = 'Add Book';
        $data['title_list'] = 'Book Details';
        $listbook = $this->book_model->listbook();
        $data['listbook'] = $listbook;
        $this->load->view('layout/student/header');
        $this->load->view('user/book/createbook', $data);
        $this->load->view('layout/student/footer');
    }

    function create() {
        $data['title'] = 'Add Book';
        $data['title_list'] = 'Book Details';
        $this->form_validation->set_rules('book_title', $this->lang->line('book_title'), 'trim|required|xss_clean');
        if ($this->form_validation->run() == FALSE) {
            $listbook = $this->book_model->listbook();
            $data['listbook'] = $listbook;
            $this->load->view('layout/header');
            $this->load->view('admin/book/createbook', $data);
            $this->load->view('layout/footer');
        } else {
            $data = array(
                'book_title'              => $this->input->post('book_title'),
                'subject'                 => $this->input->post('subject'),
                'rack_no'                 => $this->input->post('rack_no'),
                'publish'                 => $this->input->post('publish'),
                'author'                  => $this->input->post('author'),
                'qty'                     => $this->input->post('qty'),
                'perunitcost'             => $this->input->post('perunitcost'),
                'postdate'                => date('Y-m-d', $this->customlib->datetostrtotime($this->input->post('postdate'))),
                'description'             => $this->input->post('description'),
                'book_type'               => $this->input->post('book_type'),
                'book_category'           => $this->input->post('book_category'),
                'book_collection'         => $this->input->post('book_collection'),
                'book_language'           => $this->input->post('book_language'),
                'shelving_location'       => $this->input->post('shelving_location'),
                'source_of_classification'=> $this->input->post('source_of_classification'),
                'lost_status'             => $this->input->post('lost_status'),
            );
            $this->book_model->addbooks($data);
            redirect('admin/book/index');
        }
    }

    function edit($id) {

        $data['title'] = 'Edit Book';
        $data['title_list'] = 'Book Details';
        $data['id'] = $id;
        $editbook = $this->book_model->get($id);
        $data['editbook'] = $editbook;
        $this->form_validation->set_rules('book_title', $this->lang->line('book_title'), 'trim|required|xss_clean');
        if ($this->form_validation->run() == FALSE) {
            $listbook = $this->book_model->listbook();
            $data['listbook'] = $listbook;
            $this->load->view('layout/header');
            $this->load->view('admin/book/editbook', $data);
            $this->load->view('layout/footer');
        } else {
            $data = array(
                'id' => $this->input->post('id'),
                'book_title' => $this->input->post('book_title'),
                'subject' => $this->input->post('subject'),
                'rack_no' => $this->input->post('rack_no'),
                'publish' => $this->input->post('publish'),
                'author' => $this->input->post('author'),
                'qty' => $this->input->post('qty'),
                'perunitcost' => $this->input->post('perunitcost'),
                'description' => $this->input->post('description')
            );

            if (isset($_POST['postdate']) && $_POST['postdate'] != '') {
                $data['postdate'] = date('Y-m-d', $this->customlib->datetostrtotime($this->input->post('postdate')));
            } else {
                $data['postdate'] = "";
            }

            $this->book_model->addbooks($data);
            $this->session->set_flashdata('msg', '<div feemaster="alert alert-success text-center">'.$this->lang->line('book_details_added_to_database').'</div>');
            redirect('admin/book/index');
        }
    }

    function delete($id) {
        $data['title'] = 'Fees Master List';
        $this->book_model->remove($id);
        redirect('admin/book/index');
    }
 
    public function issue() 
    {
        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'book/issue');
        $data['title'] = 'Add Book';
        $data['title_list'] = 'Book Details';
        $member_type = "student";
        $student_id = $this->customlib->getStudentSessionUserID();
        $checkIsMember = $this->librarymember_model->checkIsMember($member_type, $student_id);
        if (is_array($checkIsMember)) {
            $data['bookList'] = $checkIsMember;
            $data['isCheck'] = "1";
        } else {
            $data['isCheck'] = "0";
        }
 
        $this->load->view('layout/student/header');
        $this->load->view('user/book/issue', $data);
        $this->load->view('layout/student/footer');
    }

    /**
     * Get book details for popup display (User side)
     */
    public function getBookDetails($book_id)
    {
        $book = $this->book_model->get($book_id);
        if (!$book) {
            echo json_encode(['status' => 'error', 'message' => 'Book not found']);
            return;
        }

        // Prepare book data
        $book_data = [
            'id' => $book['id'],
            'book_title' => $book['book_title'],
            'book_no' => $book['book_no'],
            'isbn_no' => $book['isbn_no'],
            'author' => $book['author'],
            'publish' => $book['publish'],
            'subject' => $book['subject'],
            'rack_no' => $book['rack_no'],
            'qty' => $book['qty'],
            'perunitcost' => $book['perunitcost'],
            'postdate' => $book['postdate'],
            'description' => $book['description'],
            'book_type' => $book['book_type'] ?? '',
            'book_category' => $book['book_category'] ?? '',
            'book_collection' => $book['book_collection'] ?? '',
            'book_language' => $book['book_language'] ?? '',
            'shelving_location' => $book['shelving_location'] ?? '',
            'source_of_classification' => $book['source_of_classification'] ?? '',
            'lost_status' => $book['lost_status'] ?? 'Available'
        ];

        echo json_encode(['status' => 'success', 'data' => $book_data]);
    }

    /**
     * Fetch book information from Google Books API (User side)
     */
    public function getGoogleBookInfo()
    {
        $isbn = $this->input->post('isbn');

        if (empty($isbn)) {
            echo json_encode(['status' => 'error', 'message' => 'ISBN is required']);
            return;
        }

        // Clean ISBN (remove any hyphens, spaces, and non-alphanumeric characters except X)
        $isbn = preg_replace('/[^0-9X]/i', '', $isbn);

        // Validate ISBN length (should be 10 or 13 digits)
        if (strlen($isbn) < 10 || strlen($isbn) > 13) {
            echo json_encode(['status' => 'error', 'message' => 'Invalid ISBN format']);
            return;
        }

        // Google Books API URL
        $api_url = "https://www.googleapis.com/books/v1/volumes?q=isbn:" . $isbn;

        // Log the API call for debugging
        log_message('info', 'Google Books API call: ' . $api_url);

        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'School ERP System/1.0');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        // Log response for debugging
        log_message('info', 'Google Books API response code: ' . $http_code);
        if ($curl_error) {
            log_message('error', 'Google Books API cURL error: ' . $curl_error);
        }

        if ($http_code !== 200 || !$response) {
            $error_msg = 'Failed to fetch data from Google Books API';
            if ($curl_error) {
                $error_msg .= ': ' . $curl_error;
            }
            echo json_encode(['status' => 'error', 'message' => $error_msg, 'http_code' => $http_code]);
            return;
        }

        $data = json_decode($response, true);

        // Log the decoded response for debugging
        log_message('info', 'Google Books API decoded response: ' . json_encode($data));

        if (!isset($data['items']) || empty($data['items'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'No book found with this ISBN: ' . $isbn,
                'api_url' => $api_url,
                'total_items' => isset($data['totalItems']) ? $data['totalItems'] : 0
            ]);
            return;
        }

        $book_info = $data['items'][0]['volumeInfo'];

        // Process image URLs to ensure HTTPS and get best quality
        $thumbnail = '';
        $smallThumbnail = '';

        // Try different image sizes in order of preference
        $imageLinks = $book_info['imageLinks'] ?? [];

        // Priority order: large, medium, thumbnail, smallThumbnail
        if (isset($imageLinks['large'])) {
            $thumbnail = $imageLinks['large'];
        } elseif (isset($imageLinks['medium'])) {
            $thumbnail = $imageLinks['medium'];
        } elseif (isset($imageLinks['thumbnail'])) {
            $thumbnail = $imageLinks['thumbnail'];
        } elseif (isset($imageLinks['smallThumbnail'])) {
            $thumbnail = $imageLinks['smallThumbnail'];
        }

        // Process the selected image URL
        if ($thumbnail) {
            // Ensure HTTPS
            $thumbnail = str_replace('http:', 'https:', $thumbnail);
            // Try to get higher resolution by modifying zoom parameter
            if (strpos($thumbnail, 'zoom=') !== false) {
                $thumbnail = preg_replace('/zoom=\d+/', 'zoom=2', $thumbnail);
            } else {
                $thumbnail .= (strpos($thumbnail, '?') !== false ? '&' : '?') . 'zoom=2';
            }
            // Remove edge parameter that might cause issues
            $thumbnail = preg_replace('/&edge=[^&]*/', '', $thumbnail);
        }

        // Also get small thumbnail as backup
        if (isset($imageLinks['smallThumbnail'])) {
            $smallThumbnail = str_replace('http:', 'https:', $imageLinks['smallThumbnail']);
        }

        // Clean and format description
        $description = '';
        if (isset($book_info['description'])) {
            $description = $book_info['description'];
            // Remove HTML tags but keep basic formatting
            $description = strip_tags($description, '<p><br><b><i><strong><em>');
            // Limit description length for better display
            if (strlen($description) > 2000) {
                $description = substr($description, 0, 2000) . '...';
            }
        }

        $result = [
            'status' => 'success',
            'data' => [
                'title' => $book_info['title'] ?? '',
                'authors' => isset($book_info['authors']) ? implode(', ', $book_info['authors']) : '',
                'publisher' => $book_info['publisher'] ?? '',
                'publishedDate' => $book_info['publishedDate'] ?? '',
                'description' => $description,
                'pageCount' => $book_info['pageCount'] ?? '',
                'categories' => isset($book_info['categories']) ? implode(', ', $book_info['categories']) : '',
                'language' => $book_info['language'] ?? '',
                'thumbnail' => $thumbnail,
                'smallThumbnail' => $smallThumbnail,
                'previewLink' => $book_info['previewLink'] ?? '',
                'infoLink' => $book_info['infoLink'] ?? '',
                'isbn' => $isbn
            ]
        ];

        echo json_encode($result);
    }

}

?>