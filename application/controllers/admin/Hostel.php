<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Hostel extends Admin_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->library('Customlib');
        $this->load->model(array("studentfeemaster_model", "hostel_model"));
        $this->sch_setting_detail = $this->setting_model->getSetting();
    }

    public function index()
    {

        if (!$this->rbac->hasPrivilege('hostel', 'can_view')) {
            access_denied();
        }
        $this->session->set_userdata('top_menu', 'Hostel');
        $this->session->set_userdata('sub_menu', 'hostel/index');
        $listhostel         = $this->hostel_model->listhostel();
        $data['listhostel'] = $listhostel;
        $ght                = $this->customlib->getHostaltype();
        $data['ght']        = $ght;
        $this->load->view('layout/header');
        $this->load->view('admin/hostel/createhostel', $data);
        $this->load->view('layout/footer');
    }

    public function create()
    {
        if (!$this->rbac->hasPrivilege('hostel', 'can_add')) {
            access_denied();
        }
        $data['title'] = 'Add Library';
        $this->form_validation->set_rules('hostel_name', $this->lang->line('hostel_name'), 'trim|required|xss_clean');
        $this->form_validation->set_rules('type', $this->lang->line('type'), 'trim|required|xss_clean');
        if ($this->form_validation->run() == false) {
            $listhostel         = $this->hostel_model->listhostel();
            $data['listhostel'] = $listhostel;
            $ght                = $this->customlib->getHostaltype();
            $data['ght']        = $ght;
            $this->load->view('layout/header');
            $this->load->view('admin/hostel/createhostel', $data);
            $this->load->view('layout/footer');
        } else {
            $data = array(
                'hostel_name' => $this->input->post('hostel_name'),
                'type'        => $this->input->post('type'),
                'address'     => $this->input->post('address'),
                'intake'      => $this->input->post('intake'),
                'description' => $this->input->post('description'),
            );
            $this->hostel_model->addhostel($data);
            $this->session->set_flashdata('msg', '<div class="alert alert-success text-left">' . $this->lang->line('success_message') . '</div>');
            redirect('admin/hostel/index');
        }
    }

    public function edit($id)
    {
        if (!$this->rbac->hasPrivilege('hostel', 'can_edit')) {
            access_denied();
        }
        $data['title']      = 'Add Hostel';
        $data['id']         = $id;
        $edithostel         = $this->hostel_model->get($id);
        $data['edithostel'] = $edithostel;
        $ght                = $this->customlib->getHostaltype();
        $data['ght']        = $ght;
        $this->form_validation->set_rules('hostel_name', $this->lang->line('hostel_name'), 'trim|required|xss_clean');
        $this->form_validation->set_rules('type', $this->lang->line('type'), 'trim|required|xss_clean');
        if ($this->form_validation->run() == false) {
            $listhostel         = $this->hostel_model->listhostel();
            $data['listhostel'] = $listhostel;
            $this->load->view('layout/header');
            $this->load->view('admin/hostel/edithostel', $data);
            $this->load->view('layout/footer');
        } else {
            $data = array(
                'id'          => $this->input->post('id'),
                'hostel_name' => $this->input->post('hostel_name'),
                'type'        => $this->input->post('type'),
                'address'     => $this->input->post('address'),
                'intake'      => $this->input->post('intake'),
                'description' => $this->input->post('description'),
            );
            $this->hostel_model->addhostel($data);
            $this->session->set_flashdata('msg', '<div class="alert alert-success text-left">' . $this->lang->line('update_message') . '</div>');
            redirect('admin/hostel/index');
        }
    }

    public function delete($id)
    {
        if (!$this->rbac->hasPrivilege('hostel', 'can_delete')) {
            access_denied();
        }
        $data['title'] = 'Fees Master List';
        $this->hostel_model->remove($id);
        redirect('admin/hostel/index');
    }

    public function feesearch()
    {
        if (!($this->rbac->hasPrivilege('hostel', 'can_view'))) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Hostel');
        $this->session->set_userdata('sub_menu', 'hostel/feesearch');
        $data['title']           = 'Hostel Fee Students';
        $class                   = $this->class_model->get();
        $data['classlist']       = $class;
        $data['sch_setting']     = $this->setting_model->getSetting();

        // Fixed fee master IDs 45, 46, 53, 54 - always show these fee groups
        $fee_session_group_ids = array(45, 46, 53, 54);

        // Get all students with fee master IDs 45, 46, 53, 54 by default
        $class_id = $this->input->post('class_id');
        $section_id = $this->input->post('section_id');

        // Get students with fee master IDs based on search criteria
        $student_fees = $this->getFeesByMasterIDs($fee_session_group_ids, $class_id, $section_id);
        $data['student_fees'] = $student_fees;

        $this->load->view('layout/header', $data);
        $this->load->view('admin/hostel/hostelSearchFee', $data);
        $this->load->view('layout/footer', $data);
    }

    private function getFeesByMasterIDs($fee_session_group_ids, $class_id = null, $section_id = null)
    {
        $current_session = $this->setting_model->getCurrentSession();

        $fee_ids_string = implode(',', $fee_session_group_ids);

        $sql = "SELECT
                    s.id as student_id,
                    s.firstname, s.middlename, s.lastname,
                    s.admission_no, s.roll_no,
                    c.class, sec.section,
                    ss.id as student_session_id,
                    fg.name as fee_group_name,
                    sfm.id as student_fees_master_id,
                    sfm.amount as fee_amount,
                    sfm.is_active as fee_status,
                    sfm.fee_session_group_id,
                    hr.room_no as hostel_room,
                    h.hostel_name,
                    COALESCE(sfd.amount_detail, '') as amount_detail
                FROM students s
                JOIN student_session ss ON s.id = ss.student_id
                JOIN classes c ON ss.class_id = c.id
                JOIN sections sec ON ss.section_id = sec.id
                JOIN student_fees_master sfm ON ss.id = sfm.student_session_id
                JOIN fee_session_groups fsg ON sfm.fee_session_group_id = fsg.id
                JOIN fee_groups fg ON fsg.fee_groups_id = fg.id
                LEFT JOIN hostel_rooms hr ON s.hostel_room_id = hr.id
                LEFT JOIN hostel h ON hr.hostel_id = h.id
                LEFT JOIN student_fees_deposite sfd ON sfm.id = sfd.student_fees_master_id
                WHERE ss.session_id = " . $current_session . "
                AND sfm.fee_session_group_id IN (" . $fee_ids_string . ")
                AND s.is_active = 'yes'";

        if (!empty($class_id)) {
            $sql .= " AND c.id = " . $class_id;
        }

        if (!empty($section_id)) {
            $sql .= " AND sec.id = " . $section_id;
        }

        $sql .= " ORDER BY c.class, sec.section, s.firstname, sfm.fee_session_group_id";

        $query = $this->db->query($sql);
        $results = $query->result_array();

        // Group results by student and calculate total paid amounts
        $student_data = array();
        foreach ($results as $result) {
            $student_key = $result['student_id'];

            if (!isset($student_data[$student_key])) {
                $student_data[$student_key] = $result;
                $student_data[$student_key]['total_paid_amount'] = 0;
            }

            // Calculate paid amount for this fee type
            $fee_type_paid = 0;
            if (!empty($result['amount_detail'])) {
                $fee_deposits = json_decode($result['amount_detail'], true);
                if (is_array($fee_deposits)) {
                    foreach ($fee_deposits as $deposit) {
                        if (isset($deposit['amount'])) {
                            $fee_type_paid += floatval($deposit['amount']);
                        }
                    }
                }
            }

            // Add to total paid amount
            $student_data[$student_key]['total_paid_amount'] += $fee_type_paid;
        }

        // Convert back to indexed array
        return array_values($student_data);
    }

}
