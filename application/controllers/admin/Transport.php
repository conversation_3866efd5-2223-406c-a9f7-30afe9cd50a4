<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Transport extends Admin_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model(array("transportfee_model", "routepickuppoint_model", "studenttransportfee_model", "pickuppoint_model", "route_model"));
        $this->sch_setting_detail = $this->setting_model->getSetting();
        $this->load->library("datatables");
    }

    public function feemaster()
    {
        if (!($this->rbac->hasPrivilege('transport_fees_master', 'can_view'))) {
            access_denied();
        }
        
        $this->session->set_userdata('top_menu', 'Transport');
        $this->session->set_userdata('sub_menu', 'transport/feemaster');
        $current_session               = $this->setting_model->getCurrentSession();
        $data                          = array();
        $month_list                    = $this->customlib->getMonthDropdown($this->sch_setting_detail->start_month);

        $data['title']                 = 'student fees';
        $data['month_list']            = $month_list;
        
         $month_list= $this->customlib->getMonthDropdown($this->sch_setting_detail->start_month);
       
        foreach($month_list as $key => $value){
            $data['transportfees'][]=(array)$this->transportfee_model->transportfesstype($current_session,$key);
        }
        
        $route_pickup_point_id         = $this->input->post('route_pickup_point_id');
        $data['route_pickup_point_id'] = $route_pickup_point_id;
        $route_pickup_point            = $this->routepickuppoint_model->get($route_pickup_point_id);
        $data['route_pickup_point']    = $route_pickup_point;

        $month_row = 1;
        foreach ($month_list as $month_key => $month_value) {
            $this->form_validation->set_rules('due_date_' . $month_row, $this->lang->line('due_date'), 'trim|required|xss_clean');
            $month_row += 1;
        }
        
        $rows        = $this->input->post('rows');
        if(!empty($rows)){
            foreach ($rows as $row_key => $row_value) {
                   
                    $fine_type =     $this->input->post('fine_type_' . $row_value);
                    if($fine_type == 'fix'){
                        $this->form_validation->set_rules('fine_amount_' . $row_value, $this->lang->line('fix_amount'), 'trim|required|numeric|xss_clean'); 
                    }elseif($fine_type == 'percentage'){
                        $this->form_validation->set_rules('percentage_' . $row_value, $this->lang->line('percentage'), 'trim|required|numeric|xss_clean'); 
                    }else{
                        
                    }            
            }
        }         
        
        if ($this->form_validation->run() == true) {
            $rows        = $this->input->post('rows');
            $insert_data = array();
            $update_data = array();

            foreach ($rows as $row_key => $row_value) {

                $prev_id = $this->input->post('prev_id_' . $row_value);
                $fine_amount    =   empty2null($this->input->post('fine_amount_' . $row_value));
                
                if($fine_amount){
                   $fine_amount =  convertCurrencyFormatToBaseAmount($fine_amount);                    
                }
                
                if ($prev_id > 0) {
                    
                    $old_update                    = array();
                    $old_update['id']              = $prev_id;
                    $old_update['month']           = $this->input->post('month_' . $row_value);
                    $old_update['due_date']        = $this->customlib->dateFormatToYYYYMMDD($this->input->post('due_date_' . $row_value));
                    $old_update['fine_type']       = $this->input->post('fine_type_' . $row_value);
                    $old_update['fine_percentage'] = empty2null($this->input->post('percentage_' . $row_value));
                    $old_update['fine_amount']     = $fine_amount;
                    $old_update['session_id']      = $current_session;
                    $update_data[]                 = $old_update;

                } else {

                    $new_insert                    = array();
                    $new_insert['month']           = $this->input->post('month_' . $row_value);
                    $new_insert['due_date']        = $this->customlib->dateFormatToYYYYMMDD($this->input->post('due_date_' . $row_value));
                    $new_insert['fine_type']       = $this->input->post('fine_type_' . $row_value);
                    $new_insert['fine_percentage'] = empty2null($this->input->post('percentage_' . $row_value));
                    $new_insert['fine_amount']     = $fine_amount;
                    $new_insert['session_id']      = $current_session;
                    $insert_data[]                 = $new_insert;
                    
                }

            }

            $this->transportfee_model->add($insert_data, $update_data);
            $this->session->set_flashdata('msg', $this->lang->line('success_message'));
            redirect('admin/transport/feemaster');
        }

        $this->load->view('layout/header');
        $this->load->view('admin/transport/feemaster', $data);
        $this->load->view('layout/footer');
    }

    public function feesearch()
    {
        if (!($this->rbac->hasPrivilege('transport_fees_master', 'can_view'))) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Transport');
        $this->session->set_userdata('sub_menu', 'transport/feesearch');
        $data['title']           = 'Transport Fee Students';
        $class                   = $this->class_model->get();
        $data['classlist']       = $class;
        $data['sch_setting']     = $this->setting_model->getSetting();

        // Get search criteria
        $class_id = $this->input->post('class_id');
        $section_id = $this->input->post('section_id');

        // Fixed fee master IDs 51, 52 - always show these fee groups
        $fee_session_group_ids = array(51, 52);

        // Get students with fee master IDs based on search criteria
        $student_fees = $this->getFeesByMasterIDs($fee_session_group_ids, $class_id, $section_id);
        $data['student_fees'] = $student_fees;



        $this->load->view('layout/header', $data);
        $this->load->view('admin/transport/transportSearchFee', $data);
        $this->load->view('layout/footer', $data);
    }

    private function getFeesByMasterIDs($fee_session_group_ids, $class_id = null, $section_id = null)
    {
        $current_session = $this->setting_model->getCurrentSession();

        $fee_ids_string = implode(',', $fee_session_group_ids);

        $sql = "SELECT
                    s.id as student_id,
                    s.firstname, s.middlename, s.lastname,
                    s.admission_no, s.roll_no,
                    c.class, sec.section,
                    ss.id as student_session_id,
                    fg.name as fee_group_name,
                    sfm.id as student_fees_master_id,
                    sfm.amount as fee_amount,
                    sfm.is_active as fee_status,
                    sfm.fee_session_group_id,
                    pp.name as pickup_point,
                    COALESCE(sfd.amount_detail, '') as amount_detail
                FROM students s
                JOIN student_session ss ON s.id = ss.student_id
                JOIN classes c ON ss.class_id = c.id
                JOIN sections sec ON ss.section_id = sec.id
                JOIN student_fees_master sfm ON ss.id = sfm.student_session_id
                JOIN fee_session_groups fsg ON sfm.fee_session_group_id = fsg.id
                JOIN fee_groups fg ON fsg.fee_groups_id = fg.id
                LEFT JOIN route_pickup_point rpp ON ss.route_pickup_point_id = rpp.id
                LEFT JOIN pickup_point pp ON rpp.pickup_point_id = pp.id
                LEFT JOIN student_fees_deposite sfd ON sfm.id = sfd.student_fees_master_id
                WHERE ss.session_id = " . $current_session . "
                AND sfm.fee_session_group_id IN (" . $fee_ids_string . ")
                AND s.is_active = 'yes'";

        if (!empty($class_id)) {
            $sql .= " AND c.id = " . $class_id;
        }

        if (!empty($section_id)) {
            $sql .= " AND sec.id = " . $section_id;
        }

        $sql .= " ORDER BY c.class, sec.section, s.firstname, sfm.fee_session_group_id";

        $query = $this->db->query($sql);
        $results = $query->result_array();

        // Group results by student and calculate total paid amounts
        $student_data = array();
        foreach ($results as $result) {
            $student_key = $result['student_id'];

            if (!isset($student_data[$student_key])) {
                $student_data[$student_key] = $result;
                $student_data[$student_key]['total_paid_amount'] = 0;
            }

            // Calculate paid amount for this fee type
            $fee_type_paid = 0;
            if (!empty($result['amount_detail'])) {
                $fee_deposits = json_decode($result['amount_detail'], true);
                if (is_array($fee_deposits)) {
                    foreach ($fee_deposits as $deposit) {
                        if (isset($deposit['amount'])) {
                            $fee_type_paid += floatval($deposit['amount']);
                        }
                    }
                }
            }

            // Add to total paid amount
            $student_data[$student_key]['total_paid_amount'] += $fee_type_paid;
        }

        // Convert back to indexed array
        return array_values($student_data);
    }



}
