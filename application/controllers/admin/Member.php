<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Member extends Admin_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->library('media_storage');
        $this->sch_setting_detail = $this->setting_model->getSetting();
    }

    public function index()
    {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'member/index');
        $data['title']      = 'Member';
        $data['title_list'] = 'Members';

        // Load the model explicitly
        $this->load->model('librarymember_model');
        $memberList = $this->librarymember_model->get();
        $data['memberList'] = $memberList;
        $data['sch_setting'] = $this->sch_setting_detail;
        $this->load->view('layout/header');
        $this->load->view('admin/librarian/index', $data);
        $this->load->view('layout/footer');
    }

    public function issue($id)
    {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'member/index');
        $data['title']        = 'Member';
        $data['title_list']   = 'Members';
        $memberList           = $this->librarymember_model->getByMemberID($id);
        $data['memberList']   = $memberList;
        $issued_books         = $this->bookissue_model->getMemberBooks($id);
        $data['issued_books'] = $issued_books;
        $bookList             = $this->book_model->get();
        
        $this->form_validation->set_rules('return_date', $this->lang->line('due_return_date'), 'trim|required|xss_clean');
        $this->form_validation->set_rules('book_id', $this->lang->line('books'), array('required', array('check_exists', array($this->bookissue_model, 'valid_check_exists')),
        )
        );
        if ($this->form_validation->run() == false) {

        } else {
            $member_id = $this->input->post('member_id');

            // Get member details to extract admission number and type
            $member = $this->librarymember_model->getByMemberID($member_id);
            if ($member) {
                $admission_no = ($member->member_type == 'student') ? $member->admission_no : $member->employee_id;
                $member_type = $member->member_type;

                $data = array(
                    'book_id'              => $this->input->post('book_id'),
                    'duereturn_date'       => date('Y-m-d', $this->customlib->datetostrtotime($this->input->post('return_date'))),
                    'issue_date'           => date('Y-m-d'),
                    'member_id'            => $this->input->post('member_id'), // Keep for backward compatibility
                    'member_admission_no'  => $admission_no,
                    'member_type'          => $member_type,
                    'is_returned'          => 0
                );
                $this->bookissue_model->add($data);
                $this->session->set_flashdata('msg', '<div class="alert alert-success text-left">' . $this->lang->line('success_message') . '</div>');
            } else {
                $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Member not found</div>');
            }
            redirect('admin/member/issue/' . $member_id);
        }
       
        
        $data['bookList']     = $bookList;
        
        $data['sch_setting'] = $this->sch_setting_detail;
        $this->load->view('layout/header');
        $this->load->view('admin/librarian/issue', $data);
        $this->load->view('layout/footer');
    }

    public function bookreturn()
    {
        $this->form_validation->set_rules('id', $this->lang->line('id'), 'required|trim|xss_clean');
        $this->form_validation->set_rules('member_id', $this->lang->line('member_id'), 'required|trim|xss_clean');
        $this->form_validation->set_rules('date', $this->lang->line('return_date'), 'required|trim|xss_clean');
        if ($this->form_validation->run() == false) {
            $data = array(
                'id'        => form_error('id'),
                'member_id' => form_error('member_id'),
                'date'      => form_error('date'),
            );
            $array = array('status' => 'fail', 'error' => $data);
            echo json_encode($array);
        } else {
            $id        = $this->input->post('id');
            $member_id = $this->input->post('member_id');
            $date      = date('Y-m-d', $this->customlib->datetostrtotime($this->input->post('date')));
            $data      = array(
                'id'          => $id,
                'return_date' => $date,
                'is_returned' => 1,
            );
            $this->bookissue_model->update($data);

            $array = array('status' => 'success', 'error' => '', 'message' => $this->lang->line('success_message'));
            echo json_encode($array);
        }
    }

    public function student()
    {
        if (!$this->rbac->hasPrivilege('add_student', 'can_view')) {
            access_denied();
        }
        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'member/student');
        $data['title']     = 'Student Search';
        $class             = $this->class_model->get();
        $data['classlist'] = $class;
        $button            = $this->input->post('search');
        if ($this->input->server('REQUEST_METHOD') == "GET") {
            $this->load->view('layout/header', $data);
            $this->load->view('admin/member/studentSearch', $data);
            $this->load->view('layout/footer', $data);
        } else {
            $class       = $this->input->post('class_id');
            $section     = $this->input->post('section_id');
            $search      = $this->input->post('search');
            $search_text = $this->input->post('search_text');
            if (isset($search)) {
                if ($search == 'search_filter') {
                    $this->form_validation->set_rules('class_id', $this->lang->line('class'), 'trim|required|xss_clean');
                    if ($this->form_validation->run() == false) {

                    } else {
                        $data['searchby']    = "filter";
                        $data['class_id']    = $this->input->post('class_id');
                        $data['section_id']  = $this->input->post('section_id');
                        $data['search_text'] = $this->input->post('search_text');
                        $resultlist          = $this->student_model->searchLibraryStudent($class, $section);

                        $data['resultlist'] = $resultlist;
                    }
                } else if ($search == 'search_full') {
                    $data['searchby']    = "text";
                    $data['class_id']    = $this->input->post('class_id');
                    $data['section_id']  = $this->input->post('section_id');
                    $data['search_text'] = trim($this->input->post('search_text'));
                    $resultlist          = $this->student_model->searchFullText($search_text);
                    $data['resultlist']  = $resultlist;
                }
            }
            $data['sch_setting'] = $this->sch_setting_detail;
            $this->load->view('layout/header', $data);
            $this->load->view('admin/member/studentSearch', $data);
            $this->load->view('layout/footer', $data);
        }
    }

    public function add()
    {
        // In the new system, students are automatically library members
        // Just return success with auto-generated library card number
        $student_id = $this->input->post('member_id');

        // Get student details to generate library card number
        $student = $this->db->select('admission_no, firstname, lastname')
                           ->from('students')
                           ->where('id', $student_id)
                           ->where('is_active', 'yes')
                           ->get()->row();

        if ($student) {
            $library_card_no = 'LIB-' . $student->admission_no;
            $array = array(
                'status' => 'success',
                'error' => '',
                'message' => 'Student is automatically a library member with card number: ' . $library_card_no,
                'inserted_id' => $student_id,
                'library_card_no' => $library_card_no
            );
        } else {
            $array = array('status' => 'fail', 'error' => 'Student not found or inactive');
        }
        echo json_encode($array);
    }

    public function check_cardno_exists()
    {
        $data['library_card_no'] = $this->security->xss_clean($this->input->post('library_card_no'));

        if ($this->librarymanagement_model->check_data_exists($data)) {
            $this->form_validation->set_message('check_cardno_exists', $this->lang->line('card_no_already_exists'));
            return false;
        } else {
            return true;
        }
    }

    public function teacher()
    {
        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'Library/member/teacher');
        $data['title']       = 'Add Teacher';
        $data['teacherlist'] = $this->teacher_model->getLibraryTeacher(); 
        $data['genderList'] = $this->customlib->getGender();         
        $this->load->view('layout/header', $data);
        $this->load->view('admin/member/teacher', $data);
        $this->load->view('layout/footer', $data);
    }

    public function searchMembers()
    {
        $query = $this->input->post('query');
        $members = array();

        if (strlen($query) >= 2) {
            // Search students
            $students = $this->db->query("
                SELECT
                    students.id as lib_member_id,
                    students.id as stu_id,
                    students.admission_no,
                    students.firstname,
                    students.lastname,
                    students.middlename,
                    'student' as member_type,
                    classes.class,
                    sections.section
                FROM students
                LEFT JOIN student_session ON student_session.student_id = students.id
                LEFT JOIN classes ON student_session.class_id = classes.id
                LEFT JOIN sections ON student_session.section_id = sections.id
                WHERE students.is_active = 'yes'
                AND (sections.section IS NULL OR sections.section != 'Pre Admission')
                AND (students.firstname LIKE '%{$query}%'
                     OR students.lastname LIKE '%{$query}%'
                     OR students.admission_no LIKE '%{$query}%')
                ORDER BY students.firstname ASC
                LIMIT 10
            ")->result_array();

            foreach ($students as &$student) {
                if ($student['class'] && $student['section']) {
                    $student['class_section'] = $student['class'] . ' (' . $student['section'] . ')';
                } else {
                    $student['class_section'] = '';
                }
            }

            // Search staff
            $this->load->model('staff_model');
            $staff_data = $this->staff_model->get();
            $staff = array();

            foreach ($staff_data as $staff_member) {
                $name = trim($staff_member['name'] . ' ' . $staff_member['surname']);
                if (stripos($name, $query) !== false ||
                    stripos($staff_member['employee_id'], $query) !== false) {
                    $staff[] = array(
                        'lib_member_id' => $staff_member['id'],
                        'admission_no' => $staff_member['employee_id'],
                        'teacher_name' => $name,
                        'member_type' => 'staff'
                    );
                }
            }

            $members = array_merge($students, array_slice($staff, 0, 5));
        }

        echo json_encode($members);
    }

    public function searchBooks()
    {
        $query = $this->input->post('query');
        $books = array();

        if (strlen($query) >= 2) {
            $books = $this->db->query("
                SELECT
                    id,
                    book_title,
                    author,
                    book_no,
                    isbn_no
                FROM books
                WHERE (book_title LIKE '%{$query}%'
                       OR author LIKE '%{$query}%'
                       OR book_no LIKE '%{$query}%'
                       OR isbn_no LIKE '%{$query}%')
                AND id NOT IN (
                    SELECT book_id FROM book_issues
                    WHERE is_returned = 0
                )
                ORDER BY book_title ASC
                LIMIT 10
            ")->result_array();
        }

        echo json_encode($books);
    }

    public function quickIssueBooks()
    {
        $member_id = $this->input->post('member_id');
        $books = $this->input->post('books');

        if (!$member_id || !$books) {
            echo json_encode(array('success' => false, 'message' => 'Invalid data'));
            return;
        }

        $this->load->model('bookissue_model');
        $this->load->model('librarymember_model');

        // Get member details
        $member = $this->librarymember_model->getByMemberID($member_id);
        if (!$member) {
            echo json_encode(array('success' => false, 'message' => 'Member not found'));
            return;
        }

        $success_count = 0;
        $errors = array();

        foreach ($books as $book) {
            try {
                // Get admission number for the member
                $admission_no = ($member->member_type == 'student') ?
                    ($member->admission_no ? $member->admission_no : 'STU-' . $member->id) :
                    ($member->employee_id ? $member->employee_id : 'STAFF-' . $member->id);

                $issue_data = array(
                    'book_id' => $book['id'],
                    'member_id' => $member_id,
                    'member_admission_no' => $admission_no,
                    'member_type' => $member->member_type,
                    'issue_date' => date('Y-m-d'),
                    'duereturn_date' => $book['return_date'],
                    'is_returned' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                );

                $this->db->insert('book_issues', $issue_data);
                $success_count++;

            } catch (Exception $e) {
                $errors[] = 'Error issuing book: ' . $book['book_title'];
            }
        }

        if ($success_count > 0) {
            echo json_encode(array(
                'success' => true,
                'message' => $success_count . ' books issued successfully'
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Failed to issue books: ' . implode(', ', $errors)
            ));
        }
    }

    public function addteacher()
    {
        // In the new system, staff are automatically library members
        // Just return success with auto-generated library card number
        $staff_id = $this->input->post('member_id');

        // Get staff details to generate library card number
        $staff = $this->db->select('employee_id, name, surname')
                         ->from('staff')
                         ->where('id', $staff_id)
                         ->where('is_active', 'yes')
                         ->get()->row();

        if ($staff) {
            $library_card_no = 'LIB-' . $staff->employee_id;
            $array = array(
                'status' => 'success',
                'error' => '',
                'message' => 'Staff member is automatically a library member with card number: ' . $library_card_no,
                'inserted_id' => $staff_id,
                'library_card_no' => $library_card_no
            );
        } else {
            $array = array('status' => 'fail', 'error' => 'Staff member not found or inactive');
        }
        echo json_encode($array);
    }

    public function surrender()
    {
        $this->form_validation->set_rules('member_id', $this->lang->line('book'), 'trim|required|xss_clean');
        if ($this->form_validation->run() == false) {

        } else {
            $member_id = $this->input->post('member_id');
              $row_affected=$this->librarymember_model->surrender($member_id);
            $array = array('status' => 'success', 'row_affected'=>$row_affected, 'error' => '', 'message' => $this->lang->line('success_message'));
            echo json_encode($array);
        }
    }

}
