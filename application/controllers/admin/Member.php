<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Member extends Admin_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->library('media_storage');
        $this->sch_setting_detail = $this->setting_model->getSetting();
    }

    public function index()
    {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'member/index');
        $data['title']      = 'Member';
        $data['title_list'] = 'Members';

        // Load the model explicitly
        $this->load->model('librarymember_model');
        $memberList = $this->librarymember_model->get();
        $data['memberList'] = $memberList;
        $data['sch_setting'] = $this->sch_setting_detail;
        $this->load->view('layout/header');
        $this->load->view('admin/librarian/index', $data);
        $this->load->view('layout/footer');
    }

    public function issue($id)
    {
        if (!$this->rbac->hasPrivilege('issue_return', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'member/index');
        $data['title']        = 'Member';
        $data['title_list']   = 'Members';
        $memberList           = $this->librarymember_model->getByMemberID($id);
        $data['memberList']   = $memberList;
        $issued_books         = $this->bookissue_model->getMemberBooks($id);
        $data['issued_books'] = $issued_books;
        $bookList             = $this->book_model->get();
        
        $this->form_validation->set_rules('return_date', $this->lang->line('due_return_date'), 'trim|required|xss_clean');
        $this->form_validation->set_rules('book_id', $this->lang->line('books'), array('required', array('check_exists', array($this->bookissue_model, 'valid_check_exists')),
        )
        );
        if ($this->form_validation->run() == false) {

        } else {
            $member_id = $this->input->post('member_id');

            // Get member details to extract admission number and type
            $member = $this->librarymember_model->getByMemberID($member_id);
            if ($member) {
                $admission_no = ($member->member_type == 'student') ? $member->admission_no : $member->employee_id;
                $member_type = $member->member_type;

                // SOLUTION: Create or get library member ID for foreign key constraint
                $library_member_id = $this->ensureLibraryMember($member_id, $admission_no, $member_type);

                if ($library_member_id) {
                    $data = array(
                        'book_id'              => $this->input->post('book_id'),
                        'duereturn_date'       => date('Y-m-d', $this->customlib->datetostrtotime($this->input->post('return_date'))),
                        'issue_date'           => date('Y-m-d'),
                        'member_id'            => $library_member_id, // Use actual library member ID
                        'member_admission_no'  => $admission_no,
                        'member_type'          => $member_type,
                        'is_returned'          => 0
                    );

                    // Use direct database insert for consistency with quick issue
                    try {
                        $result = $this->db->insert('book_issues', $data);
                        if ($result) {
                            $this->session->set_flashdata('msg', '<div class="alert alert-success text-left">' . $this->lang->line('success_message') . '</div>');
                        } else {
                            $db_error = $this->db->error();
                            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Failed to issue book: ' . $db_error['message'] . '</div>');
                        }
                    } catch (Exception $e) {
                        $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Error issuing book: ' . $e->getMessage() . '</div>');
                    }
                } else {
                    $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Failed to create library member record</div>');
                }
            } else {
                $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Member not found</div>');
            }
            redirect('admin/member/issue/' . $member_id);
        }
       
        
        $data['bookList']     = $bookList;
        
        $data['sch_setting'] = $this->sch_setting_detail;
        $this->load->view('layout/header');
        $this->load->view('admin/librarian/issue', $data);
        $this->load->view('layout/footer');
    }

    public function bookreturn()
    {
        $this->form_validation->set_rules('id', $this->lang->line('id'), 'required|trim|xss_clean');
        $this->form_validation->set_rules('member_id', $this->lang->line('member_id'), 'required|trim|xss_clean');
        $this->form_validation->set_rules('date', $this->lang->line('return_date'), 'required|trim|xss_clean');
        if ($this->form_validation->run() == false) {
            $data = array(
                'id'        => form_error('id'),
                'member_id' => form_error('member_id'),
                'date'      => form_error('date'),
            );
            $array = array('status' => 'fail', 'error' => $data);
            echo json_encode($array);
        } else {
            $id        = $this->input->post('id');
            $member_id = $this->input->post('member_id');
            $date      = date('Y-m-d', $this->customlib->datetostrtotime($this->input->post('date')));
            $data      = array(
                'id'          => $id,
                'return_date' => $date,
                'is_returned' => 1,
            );
            $this->bookissue_model->update($data);

            $array = array('status' => 'success', 'error' => '', 'message' => $this->lang->line('success_message'));
            echo json_encode($array);
        }
    }

    public function student()
    {
        if (!$this->rbac->hasPrivilege('add_student', 'can_view')) {
            access_denied();
        }
        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'member/student');
        $data['title']     = 'Student Search';
        $class             = $this->class_model->get();
        $data['classlist'] = $class;
        $button            = $this->input->post('search');
        if ($this->input->server('REQUEST_METHOD') == "GET") {
            $this->load->view('layout/header', $data);
            $this->load->view('admin/member/studentSearch', $data);
            $this->load->view('layout/footer', $data);
        } else {
            $class       = $this->input->post('class_id');
            $section     = $this->input->post('section_id');
            $search      = $this->input->post('search');
            $search_text = $this->input->post('search_text');
            if (isset($search)) {
                if ($search == 'search_filter') {
                    $this->form_validation->set_rules('class_id', $this->lang->line('class'), 'trim|required|xss_clean');
                    if ($this->form_validation->run() == false) {

                    } else {
                        $data['searchby']    = "filter";
                        $data['class_id']    = $this->input->post('class_id');
                        $data['section_id']  = $this->input->post('section_id');
                        $data['search_text'] = $this->input->post('search_text');
                        $resultlist          = $this->student_model->searchLibraryStudent($class, $section);

                        $data['resultlist'] = $resultlist;
                    }
                } else if ($search == 'search_full') {
                    $data['searchby']    = "text";
                    $data['class_id']    = $this->input->post('class_id');
                    $data['section_id']  = $this->input->post('section_id');
                    $data['search_text'] = trim($this->input->post('search_text'));
                    $resultlist          = $this->student_model->searchFullText($search_text);
                    $data['resultlist']  = $resultlist;
                }
            }
            $data['sch_setting'] = $this->sch_setting_detail;
            $this->load->view('layout/header', $data);
            $this->load->view('admin/member/studentSearch', $data);
            $this->load->view('layout/footer', $data);
        }
    }

    public function add()
    {
        // In the new system, students are automatically library members
        // Just return success with auto-generated library card number
        $student_id = $this->input->post('member_id');

        // Get student details to generate library card number
        $student = $this->db->select('admission_no, firstname, lastname')
                           ->from('students')
                           ->where('id', $student_id)
                           ->where('is_active', 'yes')
                           ->get()->row();

        if ($student) {
            $library_card_no = 'LIB-' . $student->admission_no;
            $array = array(
                'status' => 'success',
                'error' => '',
                'message' => 'Student is automatically a library member with card number: ' . $library_card_no,
                'inserted_id' => $student_id,
                'library_card_no' => $library_card_no
            );
        } else {
            $array = array('status' => 'fail', 'error' => 'Student not found or inactive');
        }
        echo json_encode($array);
    }

    public function check_cardno_exists()
    {
        $data['library_card_no'] = $this->security->xss_clean($this->input->post('library_card_no'));

        if ($this->librarymanagement_model->check_data_exists($data)) {
            $this->form_validation->set_message('check_cardno_exists', $this->lang->line('card_no_already_exists'));
            return false;
        } else {
            return true;
        }
    }

    public function teacher()
    {
        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'Library/member/teacher');
        $data['title']       = 'Add Teacher';
        $data['teacherlist'] = $this->teacher_model->getLibraryTeacher(); 
        $data['genderList'] = $this->customlib->getGender();         
        $this->load->view('layout/header', $data);
        $this->load->view('admin/member/teacher', $data);
        $this->load->view('layout/footer', $data);
    }

    public function searchMembers()
    {
        $query = $this->input->post('query');
        $members = array();

        if (strlen($query) >= 2) {
            try {
                // Search students
                $students = $this->db->query("
                    SELECT
                        students.id as lib_member_id,
                        students.id as stu_id,
                        students.admission_no,
                        students.firstname,
                        students.lastname,
                        students.middlename,
                        'student' as member_type,
                        classes.class,
                        sections.section
                    FROM students
                    LEFT JOIN student_session ON student_session.student_id = students.id
                    LEFT JOIN classes ON student_session.class_id = classes.id
                    LEFT JOIN sections ON student_session.section_id = sections.id
                    WHERE students.is_active = 'yes'
                    AND (sections.section IS NULL OR sections.section != 'Pre Admission')
                    AND (students.firstname LIKE '%{$query}%'
                         OR students.lastname LIKE '%{$query}%'
                         OR students.admission_no LIKE '%{$query}%')
                    ORDER BY students.firstname ASC
                    LIMIT 10
                ")->result_array();

                foreach ($students as &$student) {
                    if ($student['class'] && $student['section']) {
                        $student['class_section'] = $student['class'] . ' (' . $student['section'] . ')';
                    } else {
                        $student['class_section'] = '';
                    }
                }

                // Search staff
                $this->load->model('staff_model');
                $staff_data = $this->staff_model->get();
                $staff = array();

                foreach ($staff_data as $staff_member) {
                    $name = trim($staff_member['name'] . ' ' . $staff_member['surname']);
                    if (stripos($name, $query) !== false ||
                        stripos($staff_member['employee_id'], $query) !== false) {
                        $staff[] = array(
                            'lib_member_id' => $staff_member['id'],
                            'admission_no' => $staff_member['employee_id'],
                            'teacher_name' => $name,
                            'member_type' => 'staff'
                        );
                    }
                }

                $members = array_merge($students, array_slice($staff, 0, 5));
            } catch (Exception $e) {
                log_message('error', 'Member search error: ' . $e->getMessage());
            }
        }

        header('Content-Type: application/json');
        echo json_encode($members);
    }

    public function searchBooks()
    {
        $query = $this->input->post('query');
        $books = array();

        if (strlen($query) >= 2) {
            try {
                $books = $this->db->query("
                    SELECT
                        id,
                        book_title,
                        author,
                        book_no,
                        isbn_no
                    FROM books
                    WHERE (book_title LIKE '%{$query}%'
                           OR author LIKE '%{$query}%'
                           OR book_no LIKE '%{$query}%'
                           OR isbn_no LIKE '%{$query}%')
                    ORDER BY book_title ASC
                    LIMIT 10
                ")->result_array();
            } catch (Exception $e) {
                log_message('error', 'Book search error: ' . $e->getMessage());
            }
        }

        header('Content-Type: application/json');
        echo json_encode($books);
    }

    public function quickIssueBooks()
    {
        $member_id = $this->input->post('member_id');
        $books = $this->input->post('books');

        if (!$member_id || !$books) {
            echo json_encode(array('success' => false, 'message' => 'Invalid data - missing member or books'));
            return;
        }

        $this->load->model('bookissue_model');
        $this->load->model('librarymember_model');

        // Use the EXACT same method as the working issue functionality
        $member = $this->librarymember_model->getByMemberID($member_id);

        if (!$member) {
            echo json_encode(array('success' => false, 'message' => 'Member not found'));
            return;
        }

        $success_count = 0;
        $errors = array();

        foreach ($books as $book) {
            try {
                // Check book availability using the same methods as the bookissue model
                $book_id = $book['id'];

                // Check if book has available quantity
                if ($this->bookissue_model->checkAvailQuantity($book_id)) {
                    $errors[] = 'Book "' . $book['book_title'] . '" is not available (no copies left)';
                    continue;
                }

                // Check if book is already issued to this member
                if ($this->bookissue_model->checkBookIssuedOrNot($book_id, $member_id)) {
                    $errors[] = 'Book "' . $book['book_title'] . '" is already issued to this member';
                    continue;
                }

                // Get admission number and member type
                $admission_no = ($member->member_type == 'student') ? $member->admission_no : $member->employee_id;
                $member_type = $member->member_type;

                // SOLUTION: Create or get library member ID for foreign key constraint
                $library_member_id = $this->ensureLibraryMember($member_id, $admission_no, $member_type);

                if (!$library_member_id) {
                    $errors[] = 'Failed to create library member for: ' . $book['book_title'];
                    continue;
                }

                $data = array(
                    'book_id'              => $book['id'],
                    'duereturn_date'       => $book['return_date'], // Already in Y-m-d format
                    'issue_date'           => date('Y-m-d'),
                    'member_id'            => $library_member_id, // Use actual library member ID
                    'member_admission_no'  => $admission_no,
                    'member_type'          => $member_type,
                    'is_returned'          => 0
                );

                // Try direct database insert
                try {
                    $direct_result = $this->db->insert('book_issues', $data);
                    if ($direct_result) {
                        $insert_id = $this->db->insert_id();
                        $success_count++;
                        log_message('info', 'Book issued successfully: ' . $insert_id);
                    } else {
                        $db_error = $this->db->error();
                        $errors[] = 'Failed to issue: ' . $book['book_title'] . ' - ' . $db_error['message'];
                        log_message('error', 'Insert error: ' . json_encode($db_error));
                    }
                } catch (Exception $e) {
                    $errors[] = 'Exception for: ' . $book['book_title'] . ' - ' . $e->getMessage();
                    log_message('error', 'Insert exception: ' . $e->getMessage());
                }

            } catch (Exception $e) {
                $errors[] = 'Error issuing book: ' . $book['book_title'] . ' - ' . $e->getMessage();
            }
        }

        $response = array();
        if ($success_count > 0) {
            $response = array(
                'success' => true,
                'message' => $success_count . ' book(s) issued successfully'
            );
            if (!empty($errors)) {
                $response['message'] .= '. Errors: ' . implode(', ', $errors);
            }
        } else {
            $response = array(
                'success' => false,
                'message' => 'Failed to issue books: ' . implode(', ', $errors)
            );
        }

        header('Content-Type: application/json');
        echo json_encode($response);
    }

    // Enhanced debug method - remove after testing
    public function testBookIssue()
    {
        echo "<h3>Testing Book Issue System</h3>";

        // Test 1: Check if books exist
        echo "<h4>1. Checking Books Table</h4>";
        $books = $this->db->query("SELECT id, book_title FROM books LIMIT 3")->result_array();
        if (empty($books)) {
            echo "❌ No books found in database<br>";
            return;
        } else {
            echo "✅ Books found:<br>";
            foreach ($books as $book) {
                echo "- ID: {$book['id']}, Title: {$book['book_title']}<br>";
            }
        }

        // Test 2: Check if members exist
        echo "<h4>2. Checking Members</h4>";
        $this->load->model('librarymember_model');
        $members = $this->librarymember_model->get();
        if (empty($members)) {
            echo "❌ No members found<br>";
            return;
        } else {
            echo "✅ Members found: " . count($members) . "<br>";
            $test_member = $members[0];
            echo "Test member: ID={$test_member['lib_member_id']}, Name=";
            if ($test_member['member_type'] == 'student') {
                echo $test_member['firstname'] . ' ' . $test_member['lastname'];
            } else {
                echo $test_member['teacher_name'];
            }
            echo "<br>";
        }

        // Test 3: Direct database insert
        echo "<h4>3. Testing Direct Database Insert</h4>";
        $test_data = array(
            'book_id' => $books[0]['id'],
            'member_id' => $test_member['lib_member_id'],
            'member_admission_no' => $test_member['admission_no'],
            'member_type' => $test_member['member_type'],
            'issue_date' => date('Y-m-d'),
            'duereturn_date' => date('Y-m-d', strtotime('+7 days')),
            'is_returned' => 0
        );

        echo "Test data: " . json_encode($test_data) . "<br>";

        try {
            $result = $this->db->insert('book_issues', $test_data);
            if ($result) {
                $insert_id = $this->db->insert_id();
                echo "✅ Direct insert successful! Insert ID: $insert_id<br>";
            } else {
                $db_error = $this->db->error();
                echo "❌ Direct insert failed: " . json_encode($db_error) . "<br>";
            }
        } catch (Exception $e) {
            echo "❌ Exception during direct insert: " . $e->getMessage() . "<br>";
        }

        // Test 4: Using bookissue model
        echo "<h4>4. Testing Bookissue Model</h4>";
        $this->load->model('bookissue_model');

        try {
            $model_result = $this->bookissue_model->add($test_data);
            if ($model_result) {
                echo "✅ Model insert successful! Insert ID: $model_result<br>";
            } else {
                $db_error = $this->db->error();
                echo "❌ Model insert failed: " . json_encode($db_error) . "<br>";
            }
        } catch (Exception $e) {
            echo "❌ Exception during model insert: " . $e->getMessage() . "<br>";
        }

        // Test 5: Check what was actually inserted
        echo "<h4>5. Checking Database Results</h4>";
        $inserted_records = $this->db->query("SELECT * FROM book_issues ORDER BY id DESC LIMIT 3")->result_array();
        if (empty($inserted_records)) {
            echo "❌ No records found in book_issues table<br>";
        } else {
            echo "✅ Records in book_issues table:<br>";
            foreach ($inserted_records as $record) {
                echo "- ID: {$record['id']}, Book: {$record['book_id']}, Member: {$record['member_id']}, Date: {$record['issue_date']}<br>";
            }
        }

        echo "<p><strong>Remove this method after testing!</strong></p>";
    }

    /**
     * Ensure library member exists for foreign key constraint
     * Creates library member record if it doesn't exist
     */
    private function ensureLibraryMember($student_staff_id, $admission_no, $member_type)
    {
        // Check if library member already exists
        $existing = $this->db->query("
            SELECT id FROM libarary_members
            WHERE member_id = ? AND member_type = ?
        ", array($student_staff_id, $member_type))->row();

        if ($existing) {
            return $existing->id;
        }

        // Create new library member record
        $library_card_no = 'LIB-' . $admission_no;

        $library_member_data = array(
            'member_id' => $student_staff_id,
            'member_type' => $member_type,
            'library_card_no' => $library_card_no,
            'is_active' => 'yes',
            'created_at' => date('Y-m-d H:i:s')
        );

        try {
            $result = $this->db->insert('libarary_members', $library_member_data);
            if ($result) {
                $insert_id = $this->db->insert_id();
                log_message('info', 'Created library member: ' . $insert_id . ' for ' . $admission_no);
                return $insert_id;
            } else {
                $db_error = $this->db->error();
                log_message('error', 'Failed to create library member: ' . json_encode($db_error));
                return false;
            }
        } catch (Exception $e) {
            log_message('error', 'Exception creating library member: ' . $e->getMessage());
            return false;
        }
    }

    public function addteacher()
    {
        // In the new system, staff are automatically library members
        // Just return success with auto-generated library card number
        $staff_id = $this->input->post('member_id');

        // Get staff details to generate library card number
        $staff = $this->db->select('employee_id, name, surname')
                         ->from('staff')
                         ->where('id', $staff_id)
                         ->where('is_active', 'yes')
                         ->get()->row();

        if ($staff) {
            $library_card_no = 'LIB-' . $staff->employee_id;
            $array = array(
                'status' => 'success',
                'error' => '',
                'message' => 'Staff member is automatically a library member with card number: ' . $library_card_no,
                'inserted_id' => $staff_id,
                'library_card_no' => $library_card_no
            );
        } else {
            $array = array('status' => 'fail', 'error' => 'Staff member not found or inactive');
        }
        echo json_encode($array);
    }

    public function surrender()
    {
        $this->form_validation->set_rules('member_id', $this->lang->line('book'), 'trim|required|xss_clean');
        if ($this->form_validation->run() == false) {

        } else {
            $member_id = $this->input->post('member_id');
              $row_affected=$this->librarymember_model->surrender($member_id);
            $array = array('status' => 'success', 'row_affected'=>$row_affected, 'error' => '', 'message' => $this->lang->line('success_message'));
            echo json_encode($array);
        }
    }

}
