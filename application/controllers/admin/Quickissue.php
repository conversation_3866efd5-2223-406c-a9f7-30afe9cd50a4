<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Quickissue extends Admin_Controller {

    function __construct() {
        parent::__construct();
        $this->load->model('book_model');
        $this->load->model('bookissue_model');
        $this->load->model('librarymember_model');
        $this->load->model('staff_model');
    }

    public function index() {
        if (!$this->rbac->hasPrivilege('quick_issue', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Library');
        $this->session->set_userdata('sub_menu', 'library/quickissue');
        
        $data['title'] = 'Quick Issue';
        $data['title_list'] = 'Quick Book Issue';
        $data['sch_setting'] = $this->sch_setting_detail;
        
        $this->load->view('layout/header', $data);
        $this->load->view('admin/quickissue/index', $data);
        $this->load->view('layout/footer', $data);
    }

    public function searchMembers() {
        $query = $this->input->post('query');
        $members = array();

        if (strlen($query) >= 2) {
            try {
                // Search students
                $students = $this->db->query("
                    SELECT
                        students.id as lib_member_id,
                        students.id as stu_id,
                        students.admission_no,
                        students.firstname,
                        students.lastname,
                        students.middlename,
                        'student' as member_type,
                        classes.class,
                        sections.section
                    FROM students
                    LEFT JOIN student_session ON student_session.student_id = students.id
                    LEFT JOIN classes ON student_session.class_id = classes.id
                    LEFT JOIN sections ON student_session.section_id = sections.id
                    WHERE students.is_active = 'yes'
                    AND (sections.section IS NULL OR sections.section != 'Pre Admission')
                    AND (students.firstname LIKE '%{$query}%'
                         OR students.lastname LIKE '%{$query}%'
                         OR students.admission_no LIKE '%{$query}%')
                    ORDER BY students.firstname ASC
                    LIMIT 10
                ")->result_array();

                foreach ($students as &$student) {
                    if ($student['class'] && $student['section']) {
                        $student['class_section'] = $student['class'] . ' (' . $student['section'] . ')';
                    } else {
                        $student['class_section'] = '';
                    }
                }

                // Search staff
                $staff_data = $this->staff_model->get();
                $staff = array();

                foreach ($staff_data as $staff_member) {
                    $name = trim($staff_member['name'] . ' ' . $staff_member['surname']);
                    if (stripos($name, $query) !== false ||
                        stripos($staff_member['employee_id'], $query) !== false) {
                        $staff[] = array(
                            'lib_member_id' => $staff_member['id'],
                            'admission_no' => $staff_member['employee_id'],
                            'teacher_name' => $name,
                            'member_type' => 'staff'
                        );
                    }
                }

                $members = array_merge($students, array_slice($staff, 0, 5));
            } catch (Exception $e) {
                log_message('error', 'Member search error: ' . $e->getMessage());
            }
        }

        header('Content-Type: application/json');
        echo json_encode($members);
    }

    public function searchBooks() {
        $query = $this->input->post('query');
        $books = array();

        if (strlen($query) >= 2) {
            try {
                $books = $this->db->query("
                    SELECT
                        id,
                        book_title,
                        author,
                        book_no,
                        isbn_no
                    FROM books
                    WHERE (book_title LIKE '%{$query}%'
                           OR author LIKE '%{$query}%'
                           OR book_no LIKE '%{$query}%'
                           OR isbn_no LIKE '%{$query}%')
                    ORDER BY book_title ASC
                    LIMIT 10
                ")->result_array();
            } catch (Exception $e) {
                log_message('error', 'Book search error: ' . $e->getMessage());
            }
        }

        header('Content-Type: application/json');
        echo json_encode($books);
    }

    public function issueBooks() {
        $member_id = $this->input->post('member_id');
        $books = $this->input->post('books');

        if (!$member_id || !$books) {
            echo json_encode(array('success' => false, 'message' => 'Invalid data - missing member or books'));
            return;
        }

        // Use the EXACT same method as the working issue functionality
        $member = $this->librarymember_model->getByMemberID($member_id);

        if (!$member) {
            echo json_encode(array('success' => false, 'message' => 'Member not found'));
            return;
        }

        $success_count = 0;
        $errors = array();

        foreach ($books as $book) {
            try {
                // Check book availability
                $book_id = $book['id'];

                // Check if book has available quantity
                if ($this->bookissue_model->checkAvailQuantity($book_id)) {
                    $errors[] = 'Book "' . $book['book_title'] . '" is not available (no copies left)';
                    continue;
                }

                // Check if book is already issued to this member
                if ($this->bookissue_model->checkBookIssuedOrNot($book_id, $member_id)) {
                    $errors[] = 'Book "' . $book['book_title'] . '" is already issued to this member';
                    continue;
                }

                // Get admission number and member type
                $admission_no = ($member->member_type == 'student') ? $member->admission_no : $member->employee_id;
                $member_type = $member->member_type;

                // Create or get library member ID for foreign key constraint
                $library_member_id = $this->ensureLibraryMember($member_id, $admission_no, $member_type);

                if (!$library_member_id) {
                    $errors[] = 'Failed to create library member for: ' . $book['book_title'];
                    continue;
                }

                $data = array(
                    'book_id'              => $book['id'],
                    'duereturn_date'       => $book['return_date'],
                    'issue_date'           => date('Y-m-d'),
                    'member_id'            => $library_member_id,
                    'member_admission_no'  => $admission_no,
                    'member_type'          => $member_type,
                    'is_returned'          => 0
                );

                // Insert book issue
                try {
                    $result = $this->db->insert('book_issues', $data);
                    if ($result) {
                        $success_count++;
                    } else {
                        $db_error = $this->db->error();
                        $errors[] = 'Failed to issue: ' . $book['book_title'] . ' - ' . $db_error['message'];
                    }
                } catch (Exception $e) {
                    $errors[] = 'Exception for: ' . $book['book_title'] . ' - ' . $e->getMessage();
                }

            } catch (Exception $e) {
                $errors[] = 'Error issuing book: ' . $book['book_title'] . ' - ' . $e->getMessage();
            }
        }

        $response = array();
        if ($success_count > 0) {
            $response = array(
                'success' => true,
                'message' => $success_count . ' book(s) issued successfully'
            );
            if (!empty($errors)) {
                $response['message'] .= '. Errors: ' . implode(', ', $errors);
            }
        } else {
            $response = array(
                'success' => false,
                'message' => 'Failed to issue books: ' . implode(', ', $errors)
            );
        }

        header('Content-Type: application/json');
        echo json_encode($response);
    }

    /**
     * Ensure library member exists for foreign key constraint
     */
    private function ensureLibraryMember($student_staff_id, $admission_no, $member_type) {
        // Check if library member already exists
        $existing = $this->db->query("
            SELECT id FROM libarary_members
            WHERE member_id = ? AND member_type = ?
        ", array($student_staff_id, $member_type))->row();

        if ($existing) {
            return $existing->id;
        }

        // Create new library member record
        $library_card_no = 'LIB-' . $admission_no;

        $library_member_data = array(
            'member_id' => $student_staff_id,
            'member_type' => $member_type,
            'library_card_no' => $library_card_no,
            'is_active' => 'yes',
            'created_at' => date('Y-m-d H:i:s')
        );

        try {
            $result = $this->db->insert('libarary_members', $library_member_data);
            if ($result) {
                return $this->db->insert_id();
            }
        } catch (Exception $e) {
            log_message('error', 'Exception creating library member: ' . $e->getMessage());
        }
        return false;
    }
}
