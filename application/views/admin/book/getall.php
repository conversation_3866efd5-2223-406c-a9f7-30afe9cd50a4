<?php
$currency_symbol = $this->customlib->getSchoolCurrencyFormat();
?>

<div class="content-wrapper">
    <section class="content-header">
        <h1><i class="fa fa-book"></i> <?php //echo $this->lang->line('library'); ?></h1>
    </section>

    <section class="content">
        <div class="row">
            <!-- left column -->
            <div class="col-md-12">
                <!-- general form elements -->
                <div class="box box-primary" id="bklist">
                    <div class="box-header ptbnull">
                        <h3 class="box-title titlefix"><?php echo $this->lang->line('book_list'); ?></h3>
                        <div class="box-tools pull-right">
                            <?php if ($this->rbac->hasPrivilege('books', 'can_add')) {
                                ?>
                                <a href="<?php echo base_url() ?>admin/book">

                                    <button class="btn btn-primary btn-sm" autocomplete="off"><i class="fa fa-plus"></i> <?php echo $this->lang->line('add_book'); ?></button>
                                </a>
                            <?php }
                            ?>
                        </div><!-- /.pull-right -->
                    </div><!-- /.box-header -->
                    <div class="box-body">
                        <div class="mailbox-controls">
                            <!-- Check all button -->
                            <?php if ($this->session->flashdata('msg')) { ?>
                                <?php 
                                    echo $this->session->flashdata('msg');
                                    $this->session->unset_userdata('msg');
                                ?>
                            <?php } ?>
                            <?php
                            if (isset($error_message)) {
                                echo "<div class='alert alert-danger'>" . $error_message . "</div>";
                            }
                            ?> 
                        </div>
                        <div class="mailbox-messages table-responsive overflow-visible-1">
                            <table width="100%" class="table table-striped table-bordered table-hover book-list" data-export-title="<?php echo $this->lang->line('book_list'); ?>">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('book_title'); ?></th>
                                        <th><?php echo $this->lang->line('description'); ?></th>
                                        <th><?php echo $this->lang->line('book_number'); ?></th>
                                        <th><?php echo $this->lang->line('isbn_number'); ?></th>
                                        <th><?php echo $this->lang->line('publisher'); ?></th>
                                        <th><?php echo $this->lang->line('author'); ?></th>
                                        <th><?php echo $this->lang->line('subject'); ?></th>
                                        <th><?php echo $this->lang->line('rack_number'); ?></th>
                                        <th><?php echo $this->lang->line('book_type'); ?></th>
                                        <th><?php echo $this->lang->line('book_category'); ?></th>
                                        <th><?php echo $this->lang->line('book_collection'); ?></th>
                                        <th><?php echo $this->lang->line('book_language'); ?></th>
                                        <th><?php echo $this->lang->line('shelving_location'); ?></th>
                                        <th><?php echo $this->lang->line('source_of_classification'); ?></th>
                                        <th><?php echo $this->lang->line('lost_status'); ?></th>
                                        <th><?php echo $this->lang->line('qty'); ?></th>
                                        <th><?php echo $this->lang->line('available'); ?></th>
                                        <th class="text-right"><?php echo $this->lang->line('book_price'); ?></th>
                                        <th><?php echo $this->lang->line('post_date'); ?></th>
                                        <th class="no-print text text-right noExport "><?php echo $this->lang->line('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table><!-- /.table -->
                        </div><!-- /.mail-box-messages -->
                    </div><!-- /.box-body -->
                    <div class="box-footer">
                        <div class="mailbox-controls">
                            <!-- Check all button -->
                            <div class="pull-right">
                            </div><!-- /.pull-right -->
                        </div>
                    </div>
                </div>
            </div><!--/.col (left) -->
            <!-- right column -->
        </div>
        <div class="row">
            <!-- left column -->
            <!-- right column -->
            <div class="col-md-12">
                <!-- Horizontal Form -->
                <!-- general form elements disabled -->
            </div><!--/.col (right) -->
        </div>   <!-- /.row -->
    </section><!-- /.content -->
</div><!-- /.content-wrapper -->

<!-- Custom CSS for Book Details Modal -->
<style>
.book-details-link {
    color: #337ab7;
    text-decoration: none;
    cursor: pointer;
}
.book-details-link:hover {
    color: #23527c;
    text-decoration: underline;
}
.modal-lg {
    width: 90%;
    max-width: 1000px;
}
#bookDetailsModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}
#bookDetailsModal .table td {
    padding: 8px;
    border-top: 1px solid #ddd;
}
#bookDetailsModal .table td:first-child {
    width: 200px;
    font-weight: bold;
}
#bookDetailsModal .well {
    max-height: 200px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    padding: 15px;
}
.book-cover-placeholder {
    background: #f5f5f5;
    padding: 50px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    text-align: center;
}
</style>

<!-- Book Details Modal -->
<div class="modal fade" id="bookDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookDetailsModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="bookDetailsModalLabel"><?php echo $this->lang->line('book'); ?> <?php echo $this->lang->line('details'); ?></h4>
            </div>
            <div class="modal-body">
                <div id="bookDetailsContent">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin fa-2x"></i>
                        <p><?php echo $this->lang->line('please_wait'); ?>...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo $this->lang->line('close'); ?></button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    var base_url = '<?php echo base_url() ?>';
    function Popup(data)
    {
        var frame1 = $('<iframe />');
        frame1[0].name = "frame1";
        frame1.css({"position": "absolute", "top": "-1000000px"});
        $("body").append(frame1);
        var frameDoc = frame1[0].contentWindow ? frame1[0].contentWindow : frame1[0].contentDocument.document ? frame1[0].contentDocument.document : frame1[0].contentDocument;
        frameDoc.document.open();
        //Create a new HTML document.
        frameDoc.document.write('<html>');
        frameDoc.document.write('<head>');
        frameDoc.document.write('<title></title>');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/bootstrap/css/bootstrap.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/font-awesome.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/ionicons.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/AdminLTE.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/skins/_all-skins.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/iCheck/flat/blue.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/morris/morris.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/jvectormap/jquery-jvectormap-1.2.2.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/datepicker/datepicker3.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/daterangepicker/daterangepicker-bs3.css">');
        frameDoc.document.write('</head>');
        frameDoc.document.write('<body>');
        frameDoc.document.write(data);
        frameDoc.document.write('</body>');
        frameDoc.document.write('</html>');
        frameDoc.document.close();
        setTimeout(function () {
            window.frames["frame1"].focus();
            window.frames["frame1"].print();
            frame1.remove();
        }, 500);

        return true;
    }

    $("#print_div").click(function () {
        Popup($('#bklist').html());
    });   
</script>

<script>
$(document).ready(function() {
    emptyDatatable('book-list','data');
});

    ( function ( $ ) {
    'use strict';
    $(document).ready(function () {
        initDatatable('book-list','admin/book/getbooklist',[],[],100,
            [
                { "bSortable": false, "aTargets": [ -3 ] ,'sClass': 'dt-body-right'}
            ]);

        // Handle book details popup
        $(document).on('click', '.book-details-link', function(e) {
            e.preventDefault();
            var bookId = $(this).data('book-id');
            var isbn = $(this).data('isbn');

            $('#bookDetailsModal').modal('show');
            loadBookDetails(bookId, isbn);
        });
    });
    } ( jQuery ) )

    function loadBookDetails(bookId, isbn) {
        // Show loading
        $('#bookDetailsContent').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><p><?php echo $this->lang->line('please_wait'); ?>...</p></div>');

        // Fetch book details from database
        $.ajax({
            url: base_url + 'admin/book/getBookDetails/' + bookId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    var book = response.data;

                    // Fetch Google Books data if ISBN exists
                    if (isbn && isbn.trim() !== '') {
                        fetchGoogleBookInfo(book, isbn);
                    } else {
                        displayBookDetails(book, null);
                    }
                } else {
                    $('#bookDetailsContent').html('<div class="alert alert-danger">Error loading book details</div>');
                }
            },
            error: function() {
                $('#bookDetailsContent').html('<div class="alert alert-danger">Error loading book details</div>');
            }
        });
    }

    function fetchGoogleBookInfo(book, isbn) {
        console.log('Fetching Google Books info for ISBN:', isbn);

        $.ajax({
            url: base_url + 'admin/book/getGoogleBookInfo',
            type: 'POST',
            data: { isbn: isbn },
            dataType: 'json',
            success: function(response) {
                console.log('Google Books API response:', response);
                var googleData = null;
                if (response.status === 'success') {
                    googleData = response.data;
                    console.log('Google Books data found:', googleData);
                } else {
                    console.log('Google Books API error:', response.message);
                }
                displayBookDetails(book, googleData);
            },
            error: function(xhr, status, error) {
                console.log('AJAX error fetching Google Books data:', error);
                console.log('Response text:', xhr.responseText);
                displayBookDetails(book, null);
            }
        });
    }

    function displayBookDetails(book, googleData) {
        var html = '<div class="row">';

        // Left column - Book cover
        html += '<div class="col-md-4">';
        if (googleData && googleData.thumbnail && googleData.thumbnail.trim() !== '') {
            html += '<div class="text-center">';
            html += '<img src="' + googleData.thumbnail + '" class="img-responsive center-block" style="max-height: 300px; border: 1px solid #ddd; border-radius: 4px;" alt="Book Cover" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';">';
            html += '<div class="book-cover-placeholder" style="display: none; background: #f5f5f5; padding: 50px; border: 2px dashed #ddd; border-radius: 4px;">';
            html += '<i class="fa fa-book fa-5x text-muted"></i>';
            html += '<p class="text-muted">Cover Not Available</p>';
            html += '</div>';
            html += '</div>';
        } else {
            html += '<div class="text-center book-cover-placeholder" style="background: #f5f5f5; padding: 50px; border: 2px dashed #ddd; border-radius: 4px;">';
            html += '<i class="fa fa-book fa-5x text-muted"></i>';
            html += '<p class="text-muted">No Cover Image</p>';
            html += '</div>';
        }
        html += '</div>';

        // Right column - Book details
        html += '<div class="col-md-8">';
        html += '<h3>' + book.book_title + '</h3>';

        // Basic Information
        html += '<table class="table table-striped">';
        html += '<tr><td><strong><?php echo $this->lang->line('book_number'); ?>:</strong></td><td>' + (book.book_no || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('isbn_number'); ?>:</strong></td><td>' + (book.isbn_no || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('author'); ?>:</strong></td><td>' + (book.author || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('publisher'); ?>:</strong></td><td>' + (book.publish || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('subject'); ?>:</strong></td><td>' + (book.subject || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_type'); ?>:</strong></td><td>' + (book.book_type || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_category'); ?>:</strong></td><td>' + (book.book_category || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_collection'); ?>:</strong></td><td>' + (book.book_collection || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_language'); ?>:</strong></td><td>' + (book.book_language || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('rack_number'); ?>:</strong></td><td>' + (book.rack_no || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('shelving_location'); ?>:</strong></td><td>' + (book.shelving_location || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('source_of_classification'); ?>:</strong></td><td>' + (book.source_of_classification || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('qty'); ?>:</strong></td><td>' + (book.qty || '0') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_price'); ?>:</strong></td><td>' + (book.perunitcost || '0') + '</td></tr>';

        // Status with color coding
        var statusClass = 'label-default';
        switch(book.lost_status) {
            case 'Available': statusClass = 'label-success'; break;
            case 'Lost': statusClass = 'label-danger'; break;
            case 'Damaged': statusClass = 'label-warning'; break;
            case 'Missing': statusClass = 'label-danger'; break;
            case 'Under Repair': statusClass = 'label-info'; break;
        }
        html += '<tr><td><strong><?php echo $this->lang->line('lost_status'); ?>:</strong></td><td><span class="label ' + statusClass + '">' + (book.lost_status || 'Available') + '</span></td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('post_date'); ?>:</strong></td><td>' + (book.postdate || 'N/A') + '</td></tr>';
        html += '</table>';

        html += '</div>';
        html += '</div>';

        // Description section
        html += '<div class="row" style="margin-top: 20px;">';
        html += '<div class="col-md-12">';
        html += '<h4><?php echo $this->lang->line('description'); ?></h4>';

        var description = '';
        var descriptionSource = '';

        // Prioritize Google Books description
        if (googleData && googleData.description && googleData.description.trim() !== '') {
            description = googleData.description;
            descriptionSource = '<small class="text-muted"><i class="fa fa-google"></i> Description from Google Books</small>';
        } else if (book.description && book.description.trim() !== '') {
            description = book.description;
            descriptionSource = '<small class="text-muted"><i class="fa fa-database"></i> Local description</small>';
        } else {
            description = '<?php echo $this->lang->line('no_description'); ?>';
            descriptionSource = '';
        }

        html += '<div class="well" style="max-height: 300px; overflow-y: auto;">' + description + '</div>';
        if (descriptionSource) {
            html += descriptionSource;
        }
        html += '</div>';
        html += '</div>';

        // External Links Section
        html += '<div class="row" style="margin-top: 20px;">';
        html += '<div class="col-md-12">';
        html += '<h4>External Links</h4>';

        // Create Amazon India search URL
        var amazonUrl = 'https://www.amazon.in/s?k=' + encodeURIComponent(book.book_title + ' ' + (book.author || ''));
        if (book.isbn_no && book.isbn_no.trim() !== '') {
            // Use the original ISBN from database for Amazon search
            amazonUrl = 'https://www.amazon.in/s?k=' + encodeURIComponent(book.isbn_no);
        }

        // Always show Amazon button
        html += '<a href="' + amazonUrl + '" target="_blank" class="btn btn-warning" style="margin-right: 10px;"><i class="fa fa-shopping-cart"></i> View on Amazon India</a>';

        // Show Google Books preview if available
        if (googleData && googleData.previewLink) {
            html += '<a href="' + googleData.previewLink + '" target="_blank" class="btn btn-primary"><i class="fa fa-google"></i> Preview on Google Books</a>';
        }

        html += '</div>';
        html += '</div>';

        // Google Books additional info
        if (googleData) {
            html += '<div class="row" style="margin-top: 20px;">';
            html += '<div class="col-md-12">';
            html += '<h4>Additional Information from Google Books</h4>';
            html += '<table class="table table-striped">';
            if (googleData.publishedDate) {
                html += '<tr><td><strong>Published Date:</strong></td><td>' + googleData.publishedDate + '</td></tr>';
            }
            if (googleData.pageCount) {
                html += '<tr><td><strong>Page Count:</strong></td><td>' + googleData.pageCount + '</td></tr>';
            }
            if (googleData.categories) {
                html += '<tr><td><strong>Categories:</strong></td><td>' + googleData.categories + '</td></tr>';
            }
            if (googleData.language) {
                html += '<tr><td><strong>Language:</strong></td><td>' + googleData.language.toUpperCase() + '</td></tr>';
            }
            html += '</table>';
            html += '</div>';
            html += '</div>';
        }

        $('#bookDetailsContent').html(html);
    }
</script>