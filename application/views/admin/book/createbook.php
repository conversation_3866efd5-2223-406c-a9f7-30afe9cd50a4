<?php
$currency_symbol = $this->customlib->getSchoolCurrencyFormat();
?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <h1>
            <i class="fa fa-book"></i> </h1>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <!-- Horizontal Form -->
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('add_book'); ?></h3>
                        <div class="box-tools pull-right">
                            <?php if ($this->rbac->hasPrivilege('import_book', 'can_view')) {
                                ?>
                                <a class="btn btn-sm btn-primary" href="<?php echo base_url(); ?>admin/book/import" autocomplete="off"><i class="fa fa-plus"></i> <?php echo $this->lang->line('import_book'); ?></a> 
                            <?php }
                            ?>
                        </div>
                    </div><!-- /.box-header -->
                    <!-- form start -->
                    <form id="form1" action="<?php echo site_url('admin/book/create') ?>"  id="employeeform" name="employeeform" method="post" accept-charset="utf-8">
                        <div class="box-body row">
                            <?php if ($this->session->flashdata('msg')) { ?>
                                <?php 
                                    echo $this->session->flashdata('msg');
                                    $this->session->unset_userdata('msg');
                                ?>
                            <?php } ?>
                            <?php
                            if (isset($error_message)) {
                                echo "<div class='alert alert-danger'>" . $error_message . "</div>";
                            }
                            ?>      
                            <?php echo $this->customlib->getCSRF(); ?>                     
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('book_title'); ?></label><small class="req"> *</small>
                                <input autofocus=""  id="book_title" name="book_title" placeholder="" type="text" class="form-control"  value="<?php echo set_value('book_title'); ?>" />
                                <span class="text-danger"><?php echo form_error('book_title'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('book_number'); ?></label>
                                <input id="book_no" name="book_no" placeholder="" type="text" class="form-control"  value="<?php echo set_value('book_no'); ?>" />
                                <span class="text-danger"><?php echo form_error('book_no'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('isbn_number'); ?></label>
                                <input id="isbn_no" name="isbn_no" placeholder="" type="text" class="form-control"  value="<?php echo set_value('isbn_no'); ?>" />
                                <span class="text-danger"><?php echo form_error('isbn_no'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('publisher'); ?></label>
                                <input id="publish" name="publish" placeholder="" type="text" class="form-control"  value="<?php echo set_value('publish'); ?>" />
                                <span class="text-danger"><?php echo form_error('publish'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('author'); ?></label>
                                <input id="author" name="author" placeholder="" type="text" class="form-control"  value="<?php echo set_value('author'); ?>" />
                                <span class="text-danger"><?php echo form_error('author'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('subject'); ?></label>
                                <input id="subject" name="subject" placeholder="" type="text" class="form-control"  value="<?php echo set_value('subject'); ?>" />
                                <span class="text-danger"><?php echo form_error('subject'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('rack_number'); ?></label>
                                <input id="rack_no" name="rack_no" placeholder="" type="text" class="form-control"  value="<?php echo set_value('rack_no'); ?>" />
                                <span class="text-danger"><?php echo form_error('rack_no'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('qty'); ?></label>
                                <input id="qty" name="qty" placeholder="" type="text" class="form-control"  value="<?php echo set_value('qty'); ?>" />
                                <span class="text-danger"><?php echo form_error('qty'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('book_price'); ?> (<?php echo $currency_symbol; ?>)</label>
                                <input id="perunitcost" name="perunitcost" placeholder="" type="text" class="form-control"  value="<?php echo set_value('perunitcost'); ?>" />
                                <span class="text-danger"><?php echo form_error('perunitcost'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('post_date'); ?></label>
                                <input id="postdate" name="postdate"  placeholder="" type="text" class="form-control date"  value="<?php echo set_value('postdate', date($this->customlib->getSchoolDateFormat())); ?>" />
                                <span class="text-danger"><?php echo form_error('postdate'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="book_type"><?php echo $this->lang->line('book_type'); ?></label>
                                <select id="book_type" name="book_type" class="form-control">
                                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                                    <option value="Fiction" <?php echo set_select('book_type', 'Fiction'); ?>>Fiction</option>
                                    <option value="Non-Fiction" <?php echo set_select('book_type', 'Non-Fiction'); ?>>Non-Fiction</option>
                                    <option value="Reference" <?php echo set_select('book_type', 'Reference'); ?>>Reference</option>
                                    <option value="Textbook" <?php echo set_select('book_type', 'Textbook'); ?>>Textbook</option>
                                    <option value="Journal" <?php echo set_select('book_type', 'Journal'); ?>>Journal</option>
                                    <option value="Magazine" <?php echo set_select('book_type', 'Magazine'); ?>>Magazine</option>
                                    <option value="Newspaper" <?php echo set_select('book_type', 'Newspaper'); ?>>Newspaper</option>
                                    <option value="E-Book" <?php echo set_select('book_type', 'E-Book'); ?>>E-Book</option>
                                </select>
                                <span class="text-danger"><?php echo form_error('book_type'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="book_category"><?php echo $this->lang->line('book_category'); ?></label>
                                <select id="book_category" name="book_category" class="form-control">
                                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                                    <option value="Science" <?php echo set_select('book_category', 'Science'); ?>>Science</option>
                                    <option value="Mathematics" <?php echo set_select('book_category', 'Mathematics'); ?>>Mathematics</option>
                                    <option value="Literature" <?php echo set_select('book_category', 'Literature'); ?>>Literature</option>
                                    <option value="History" <?php echo set_select('book_category', 'History'); ?>>History</option>
                                    <option value="Geography" <?php echo set_select('book_category', 'Geography'); ?>>Geography</option>
                                    <option value="Arts" <?php echo set_select('book_category', 'Arts'); ?>>Arts</option>
                                    <option value="Technology" <?php echo set_select('book_category', 'Technology'); ?>>Technology</option>
                                    <option value="Philosophy" <?php echo set_select('book_category', 'Philosophy'); ?>>Philosophy</option>
                                    <option value="Religion" <?php echo set_select('book_category', 'Religion'); ?>>Religion</option>
                                    <option value="Social Sciences" <?php echo set_select('book_category', 'Social Sciences'); ?>>Social Sciences</option>
                                    <option value="Language" <?php echo set_select('book_category', 'Language'); ?>>Language</option>
                                    <option value="Biography" <?php echo set_select('book_category', 'Biography'); ?>>Biography</option>
                                </select>
                                <span class="text-danger"><?php echo form_error('book_category'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="book_collection"><?php echo $this->lang->line('book_collection'); ?></label>
                                <select id="book_collection" name="book_collection" class="form-control">
                                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                                    <option value="Main Collection" <?php echo set_select('book_collection', 'Main Collection'); ?>>Main Collection</option>
                                    <option value="Reference Collection" <?php echo set_select('book_collection', 'Reference Collection'); ?>>Reference Collection</option>
                                    <option value="Special Collection" <?php echo set_select('book_collection', 'Special Collection'); ?>>Special Collection</option>
                                    <option value="Rare Books" <?php echo set_select('book_collection', 'Rare Books'); ?>>Rare Books</option>
                                    <option value="Children's Collection" <?php echo set_select('book_collection', 'Children\'s Collection'); ?>>Children's Collection</option>
                                    <option value="Digital Collection" <?php echo set_select('book_collection', 'Digital Collection'); ?>>Digital Collection</option>
                                    <option value="Periodicals" <?php echo set_select('book_collection', 'Periodicals'); ?>>Periodicals</option>
                                    <option value="Archives" <?php echo set_select('book_collection', 'Archives'); ?>>Archives</option>
                                </select>
                                <span class="text-danger"><?php echo form_error('book_collection'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="book_language"><?php echo $this->lang->line('book_language'); ?></label>
                                <select id="book_language" name="book_language" class="form-control">
                                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                                    <option value="English" <?php echo set_select('book_language', 'English'); ?>>English</option>
                                    <option value="Hindi" <?php echo set_select('book_language', 'Hindi'); ?>>Hindi</option>
                                    <option value="Bengali" <?php echo set_select('book_language', 'Bengali'); ?>>Bengali</option>
                                    <option value="Tamil" <?php echo set_select('book_language', 'Tamil'); ?>>Tamil</option>
                                    <option value="Telugu" <?php echo set_select('book_language', 'Telugu'); ?>>Telugu</option>
                                    <option value="Marathi" <?php echo set_select('book_language', 'Marathi'); ?>>Marathi</option>
                                    <option value="Gujarati" <?php echo set_select('book_language', 'Gujarati'); ?>>Gujarati</option>
                                    <option value="Kannada" <?php echo set_select('book_language', 'Kannada'); ?>>Kannada</option>
                                    <option value="Malayalam" <?php echo set_select('book_language', 'Malayalam'); ?>>Malayalam</option>
                                    <option value="Punjabi" <?php echo set_select('book_language', 'Punjabi'); ?>>Punjabi</option>
                                    <option value="Urdu" <?php echo set_select('book_language', 'Urdu'); ?>>Urdu</option>
                                    <option value="Sanskrit" <?php echo set_select('book_language', 'Sanskrit'); ?>>Sanskrit</option>
                                    <option value="French" <?php echo set_select('book_language', 'French'); ?>>French</option>
                                    <option value="German" <?php echo set_select('book_language', 'German'); ?>>German</option>
                                    <option value="Spanish" <?php echo set_select('book_language', 'Spanish'); ?>>Spanish</option>
                                    <option value="Other" <?php echo set_select('book_language', 'Other'); ?>>Other</option>
                                </select>
                                <span class="text-danger"><?php echo form_error('book_language'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="shelving_location"><?php echo $this->lang->line('shelving_location'); ?></label>
                                <input id="shelving_location" name="shelving_location" placeholder="e.g., Floor 2, Section A, Shelf 15" type="text" class="form-control" value="<?php echo set_value('shelving_location'); ?>" />
                                <span class="text-danger"><?php echo form_error('shelving_location'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="source_of_classification"><?php echo $this->lang->line('source_of_classification'); ?></label>
                                <select id="source_of_classification" name="source_of_classification" class="form-control">
                                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                                    <option value="Dewey Decimal Classification" <?php echo set_select('source_of_classification', 'Dewey Decimal Classification'); ?>>Dewey Decimal Classification (DDC)</option>
                                    <option value="Library of Congress Classification" <?php echo set_select('source_of_classification', 'Library of Congress Classification'); ?>>Library of Congress Classification (LCC)</option>
                                    <option value="Universal Decimal Classification" <?php echo set_select('source_of_classification', 'Universal Decimal Classification'); ?>>Universal Decimal Classification (UDC)</option>
                                    <option value="Colon Classification" <?php echo set_select('source_of_classification', 'Colon Classification'); ?>>Colon Classification (CC)</option>
                                    <option value="Custom Classification" <?php echo set_select('source_of_classification', 'Custom Classification'); ?>>Custom Classification</option>
                                    <option value="No Classification" <?php echo set_select('source_of_classification', 'No Classification'); ?>>No Classification</option>
                                </select>
                                <span class="text-danger"><?php echo form_error('source_of_classification'); ?></span>
                            </div>
                            <div class="clearfix"></div>
                            <div class="form-group col-md-6">
                                <label for="lost_status"><?php echo $this->lang->line('lost_status'); ?></label>
                                <select id="lost_status" name="lost_status" class="form-control">
                                    <option value="Available" <?php echo set_select('lost_status', 'Available', TRUE); ?>>Available</option>
                                    <option value="Lost" <?php echo set_select('lost_status', 'Lost'); ?>>Lost</option>
                                    <option value="Damaged" <?php echo set_select('lost_status', 'Damaged'); ?>>Damaged</option>
                                    <option value="Missing" <?php echo set_select('lost_status', 'Missing'); ?>>Missing</option>
                                    <option value="Under Repair" <?php echo set_select('lost_status', 'Under Repair'); ?>>Under Repair</option>
                                </select>
                                <span class="text-danger"><?php echo form_error('lost_status'); ?></span>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('description'); ?></label>
                                <textarea class="form-control" id="description" name="description" placeholder="" rows="3" placeholder="Enter ..."><?php echo set_value('description'); ?></textarea>
                                <span class="text-danger"><?php echo form_error('description'); ?></span>
                            </div>
                        </div><!-- /.box-body -->
                        <div class="box-footer">
                            <button type="submit" class="btn btn-info pull-right"><?php echo $this->lang->line('save'); ?></button>
                        </div>
                    </form>
                </div>
            </div><!--/.col (right) -->
        </div>
        <div class="row">
            <div class="col-md-12">
            </div><!--/.col (right) -->
        </div>   <!-- /.row -->
    </section><!-- /.content -->
</div><!-- /.content-wrapper -->

<script type="text/javascript">
    $(document).ready(function () {
        $("#btnreset").click(function () {
            /* Single line Reset function executes on click of Reset Button */
            $("#form1")[0].reset();
        });

    });
</script>
<script>
    $(document).ready(function () {
        $('.detail_popover').popover({
            placement: 'right',
            trigger: 'hover',
            container: 'body',
            html: true,
            content: function () {
                return $(this).closest('td').find('.fee_detail_popover').html();
            }
        });
    });
</script>
<script type="text/javascript" src="<?php echo base_url(); ?>backend/dist/js/savemode.js"></script>