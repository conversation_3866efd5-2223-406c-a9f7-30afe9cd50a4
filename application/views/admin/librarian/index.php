<style type="text/css">
    @media print
    {
        .no-print, .no-print *
        {
            display: none !important;
        }
    }
</style>
<div class="content-wrapper" style="min-height: 946px;">
    <section class="content-header">
        <h1><i class="fa fa-book"></i> <?php //echo $this->lang->line('library'); ?> </h1>
    </section>
    <!-- Main content -->
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <!-- Quick Issue Section -->
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-flash"></i> <?php echo $this->lang->line('quick'); ?> <?php echo $this->lang->line('issue'); ?></h3>
                    </div>
                    <div class="box-body">
                        <form id="quickIssueForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('member'); ?> <?php echo $this->lang->line('name'); ?></label>
                                        <input type="text" class="form-control" id="memberSearch" placeholder="<?php echo $this->lang->line('search'); ?> <?php echo $this->lang->line('member'); ?>..." autocomplete="off">
                                        <input type="hidden" id="selectedMemberId" name="member_id">
                                        <div id="memberSearchResults" class="search-results"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('book'); ?></label>
                                        <input type="text" class="form-control" id="bookSearch" placeholder="<?php echo $this->lang->line('search'); ?> <?php echo $this->lang->line('book'); ?>..." autocomplete="off" disabled>
                                        <div id="bookSearchResults" class="search-results"></div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('return_date'); ?></label>
                                        <input type="date" class="form-control" id="returnDate" name="return_date" disabled>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <button type="button" class="btn btn-primary btn-block" id="addBookBtn" disabled>
                                            <i class="fa fa-plus"></i> <?php echo $this->lang->line('add'); ?> <?php echo $this->lang->line('book'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <!-- Selected Books List -->
                        <div id="selectedBooksSection" style="display: none;">
                            <h4><?php echo $this->lang->line('selected'); ?> <?php echo $this->lang->line('books'); ?></h4>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="selectedBooksTable">
                                    <thead>
                                        <tr>
                                            <th><?php echo $this->lang->line('book'); ?> <?php echo $this->lang->line('title'); ?></th>
                                            <th><?php echo $this->lang->line('author'); ?></th>
                                            <th><?php echo $this->lang->line('book_no'); ?></th>
                                            <th><?php echo $this->lang->line('return_date'); ?></th>
                                            <th><?php echo $this->lang->line('action'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody id="selectedBooksBody">
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-right">
                                <button type="button" class="btn btn-success btn-lg" id="issueAllBooksBtn">
                                    <i class="fa fa-check"></i> <?php echo $this->lang->line('issue'); ?> <?php echo $this->lang->line('all'); ?> <?php echo $this->lang->line('books'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Members List -->
                <div class="box box-primary" id="tachelist">
                    <div class="box-header ptbnull">
                        <h3 class="box-title titlefix"><?php echo $this->lang->line('members'); ?></h3>
                        <div class="box-tools pull-right">

                        </div>
                    </div>
                    <div class="box-body">
                        <div class="mailbox-controls">
                        </div>
                        <div class="table-responsive mailbox-messages">
                            <div class="download_label"><?php echo $this->lang->line('members'); ?></div>
                            <table class="table table-striped table-bordered table-hover example" id="members">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('library_card_no'); ?></th>
                                        <th><?php echo $this->lang->line('admission_no'); ?></th>
                                        <th><?php echo $this->lang->line('name'); ?></th>
                                        <th><?php echo $this->lang->line('member_type'); ?></th>
                                        <th><?php echo $this->lang->line('class'); ?> & <?php echo $this->lang->line('section'); ?></th>
                                        <th><?php echo $this->lang->line('phone'); ?></th>
                                        <th class="text-right noExport"><?php echo $this->lang->line('action'); ?>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
if (!empty($memberList)) {
    $count = 1;

    foreach ($memberList as $member) {

        if ($member['member_type'] == "student") {
            $name  = $this->customlib->getFullName($member['firstname'], $member['middlename'], $member['lastname'], $sch_setting->middlename, $sch_setting->lastname) ;
            $phone = $member['guardian_phone'];
        } else {
            $email = $member['teacher_email'];
            $name  = $member['teacher_name'] . " (" . $member['emp_id'] . ")";
            $sex   = $member['teacher_sex'];
            $phone = $member['teacher_phone'];
        }
        ?>
                                            <tr>
                                                <td>
                                                    <?php echo $member['library_card_no']; ?>
                                                </td>
                                                <td>
                                                    <?php echo $member['admission_no']; ?>
                                                </td>
                                                <td>
                                                    <?php echo $name; ?>
                                                </td>
                                                <td>
                                                    <?php echo $this->lang->line($member['member_type']); ?>
                                                </td>
                                                <td>
                                                    <?php echo isset($member['class_section']) ? $member['class_section'] : ''; ?>
                                                </td>
                                                <td>
                                                    <?php echo $phone; ?>
                                                </td>
                                                <td class="mailbox-date pull-right">
                                                    <a href="<?php echo base_url(); ?>admin/member/issue/<?php echo $member['lib_member_id'] ?>" class="btn btn-default btn-xs"  data-toggle="tooltip" title="<?php echo $this->lang->line('issue_return'); ?>">
                                                        <i class="fa fa-sign-out"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php
}
    $count++;
}
?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.search-results {
    position: absolute;
    z-index: 1000;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    width: 100%;
    display: none;
}

.search-result-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.search-result-item:hover {
    background-color: #f5f5f5;
}

.search-result-item:last-child {
    border-bottom: none;
}

.form-group {
    position: relative;
}

#selectedBooksSection {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #eee;
}
</style>

<script>
$(document).ready(function() {
    var selectedBooks = [];
    var selectedMember = null;

    // Member search functionality
    $('#memberSearch').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            $.ajax({
                url: '<?php echo base_url(); ?>admin/member/searchMembers',
                method: 'POST',
                data: { query: query },
                dataType: 'json',
                success: function(response) {
                    displayMemberResults(response);
                }
            });
        } else {
            $('#memberSearchResults').hide();
        }
    });

    // Book search functionality
    $('#bookSearch').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            $.ajax({
                url: '<?php echo base_url(); ?>admin/member/searchBooks',
                method: 'POST',
                data: { query: query },
                dataType: 'json',
                success: function(response) {
                    displayBookResults(response);
                }
            });
        } else {
            $('#bookSearchResults').hide();
        }
    });

    // Add book to list
    $('#addBookBtn').click(function() {
        var selectedBook = $('#bookSearch').data('selectedBook');
        var returnDate = $('#returnDate').val();

        if (selectedBook && returnDate) {
            // Check if book already added
            var bookExists = selectedBooks.find(book => book.id === selectedBook.id);
            if (!bookExists) {
                selectedBook.return_date = returnDate;
                selectedBooks.push(selectedBook);
                updateSelectedBooksTable();

                // Clear book search
                $('#bookSearch').val('');
                $('#bookSearch').removeData('selectedBook');
                $('#returnDate').val('');
                $('#bookSearchResults').hide();
            } else {
                alert('<?php echo $this->lang->line('book'); ?> already added to the list!');
            }
        } else {
            alert('Please select a book and return date!');
        }
    });

    // Issue all books
    $('#issueAllBooksBtn').click(function() {
        if (selectedMember && selectedBooks.length > 0) {
            $.ajax({
                url: '<?php echo base_url(); ?>admin/member/quickIssueBooks',
                method: 'POST',
                data: {
                    member_id: selectedMember.id,
                    books: selectedBooks
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('Books issued successfully!');
                        resetQuickIssue();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('Error issuing books. Please try again.');
                }
            });
        }
    });

    // Display member search results
    function displayMemberResults(members) {
        var html = '';
        members.forEach(function(member) {
            var name = member.member_type === 'student' ?
                member.firstname + ' ' + member.lastname :
                member.teacher_name;
            var info = member.member_type === 'student' ?
                member.admission_no + ' - ' + (member.class_section || '') :
                member.admission_no;

            html += '<div class="search-result-item" data-member=\'' + JSON.stringify(member) + '\'>';
            html += '<strong>' + name + '</strong><br>';
            html += '<small>' + info + ' (' + member.member_type + ')</small>';
            html += '</div>';
        });

        $('#memberSearchResults').html(html).show();
    }

    // Display book search results
    function displayBookResults(books) {
        var html = '';
        books.forEach(function(book) {
            html += '<div class="search-result-item" data-book=\'' + JSON.stringify(book) + '\'>';
            html += '<strong>' + book.book_title + '</strong><br>';
            html += '<small>' + book.author + ' - ' + book.book_no + '</small>';
            html += '</div>';
        });

        $('#bookSearchResults').html(html).show();
    }

    // Handle member selection
    $(document).on('click', '#memberSearchResults .search-result-item', function() {
        var member = JSON.parse($(this).attr('data-member'));
        selectedMember = member;

        var name = member.member_type === 'student' ?
            member.firstname + ' ' + member.lastname :
            member.teacher_name;

        $('#memberSearch').val(name);
        $('#selectedMemberId').val(member.lib_member_id);
        $('#memberSearchResults').hide();

        // Enable book search
        $('#bookSearch').prop('disabled', false);
        $('#returnDate').prop('disabled', false);

        // Set default return date (2 weeks from today)
        var defaultDate = new Date();
        defaultDate.setDate(defaultDate.getDate() + 14);
        $('#returnDate').val(defaultDate.toISOString().split('T')[0]);
    });

    // Handle book selection
    $(document).on('click', '#bookSearchResults .search-result-item', function() {
        var book = JSON.parse($(this).attr('data-book'));

        $('#bookSearch').val(book.book_title);
        $('#bookSearch').data('selectedBook', book);
        $('#bookSearchResults').hide();

        // Enable add button
        $('#addBookBtn').prop('disabled', false);
    });

    // Update selected books table
    function updateSelectedBooksTable() {
        var html = '';
        selectedBooks.forEach(function(book, index) {
            html += '<tr>';
            html += '<td>' + book.book_title + '</td>';
            html += '<td>' + book.author + '</td>';
            html += '<td>' + book.book_no + '</td>';
            html += '<td>' + book.return_date + '</td>';
            html += '<td><button type="button" class="btn btn-danger btn-xs remove-book" data-index="' + index + '"><i class="fa fa-trash"></i></button></td>';
            html += '</tr>';
        });

        $('#selectedBooksBody').html(html);

        if (selectedBooks.length > 0) {
            $('#selectedBooksSection').show();
        } else {
            $('#selectedBooksSection').hide();
        }
    }

    // Remove book from list
    $(document).on('click', '.remove-book', function() {
        var index = $(this).data('index');
        selectedBooks.splice(index, 1);
        updateSelectedBooksTable();
    });

    // Reset quick issue form
    function resetQuickIssue() {
        selectedBooks = [];
        selectedMember = null;
        $('#quickIssueForm')[0].reset();
        $('#selectedMemberId').val('');
        $('#bookSearch').prop('disabled', true);
        $('#returnDate').prop('disabled', true);
        $('#addBookBtn').prop('disabled', true);
        $('#selectedBooksSection').hide();
        $('#memberSearchResults').hide();
        $('#bookSearchResults').hide();
    }

    // Hide search results when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('.form-group').length) {
            $('.search-results').hide();
        }
    });
});
</script>
