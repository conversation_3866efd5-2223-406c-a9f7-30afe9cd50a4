<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-flash"></i> Quick Issue <small><?php echo $this->lang->line('student_information'); ?></small>
        </h1>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-flash"></i> Quick Book Issue</h3>
                    </div>
                    <div class="box-body">
                        <!-- Notification Area -->
                        <div id="notificationArea" style="display: none; margin-bottom: 20px;">
                            <div class="alert alert-dismissible" id="notificationMessage">
                                <button type="button" class="close" onclick="hideNotification()" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                <span id="notificationText"></span>
                            </div>
                        </div>

                        <form id="quickIssueForm">
                            <input type="hidden" id="selectedMemberId" name="member_id" value="">

                            <!-- Main Search Row -->
                            <div class="row">
                                <!-- Member Search -->
                                <div class="col-md-4 col-sm-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('member'); ?> <span class="req">*</span></label>
                                        <div class="search-container">
                                            <input type="text" id="memberSearch" class="form-control" placeholder="<?php echo $this->lang->line('search'); ?> <?php echo $this->lang->line('student'); ?> or <?php echo $this->lang->line('staff'); ?>..." autocomplete="off">
                                            <div id="memberSearchResults" class="search-results"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Book Search -->
                                <div class="col-md-4 col-sm-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('book'); ?> <span class="req">*</span></label>
                                        <div class="search-container">
                                            <input type="text" id="bookSearch" class="form-control" placeholder="<?php echo $this->lang->line('search'); ?> <?php echo $this->lang->line('book'); ?>..." autocomplete="off" disabled>
                                            <div id="bookSearchResults" class="search-results"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Return Date -->
                                <div class="col-md-4 col-sm-12">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('return_date'); ?> <span class="req">*</span></label>
                                        <input type="date" id="returnDate" class="form-control" disabled>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons Row -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group text-center">
                                        <div class="btn-group-responsive">
                                            <button type="button" id="addBookBtn" class="btn btn-success btn-sm" disabled title="Add book to list">
                                                <i class="fa fa-plus"></i> Add to List
                                            </button>
                                            <button type="button" id="issueBookBtn" class="btn btn-info btn-sm" disabled title="Issue single book immediately">
                                                <i class="fa fa-flash"></i> <?php echo $this->lang->line('issue'); ?> Book
                                            </button>
                                            <button type="button" id="issueAllBooksBtn" class="btn btn-primary btn-sm" disabled title="Issue all books in the list">
                                                <i class="fa fa-check"></i> <?php echo $this->lang->line('issue'); ?> All Books
                                            </button>
                                            <button type="button" id="resetBtn" class="btn btn-default btn-sm" title="Reset form">
                                                <i class="fa fa-refresh"></i> <?php echo $this->lang->line('reset'); ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <!-- Selected Books Section -->
                        <div id="selectedBooksSection" class="row" style="display: none;">
                            <div class="col-md-12">
                                <h4><i class="fa fa-list"></i> <?php echo $this->lang->line('selected'); ?> <?php echo $this->lang->line('books'); ?></h4>
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th><?php echo $this->lang->line('book_title'); ?></th>
                                                <th><?php echo $this->lang->line('author'); ?></th>
                                                <th><?php echo $this->lang->line('book_no'); ?></th>
                                                <th><?php echo $this->lang->line('return_date'); ?></th>
                                                <th><?php echo $this->lang->line('action'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody id="selectedBooksTable">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* Search Container */
.search-container {
    position: relative;
}

/* Search Results Dropdown */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 250px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-result-item {
    padding: 12px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.member-info, .book-info {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.member-details, .book-details {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
}

.req {
    color: #e74c3c;
}

/* Button Group Responsive */
.btn-group-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.btn-group-responsive .btn {
    flex: 0 1 auto;
    min-width: 120px;
    margin: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn-group-responsive {
        flex-direction: column;
        align-items: center;
    }

    .btn-group-responsive .btn {
        width: 100%;
        max-width: 250px;
        margin: 3px 0;
    }

    .search-results {
        font-size: 13px;
    }

    .member-info, .book-info {
        font-size: 13px;
    }

    .member-details, .book-details {
        font-size: 11px;
    }
}

@media (max-width: 576px) {
    .form-group {
        margin-bottom: 15px;
    }

    .btn-group-responsive .btn {
        font-size: 13px;
        padding: 8px 12px;
    }
}

/* Selected Books Section */
#selectedBooksSection {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

#selectedBooksSection h4 {
    color: #495057;
    margin-bottom: 15px;
}

/* Table Responsive */
.table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

/* Input Focus States */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Disabled State */
.form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.7;
}

/* Button States */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-success:not(:disabled):hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-info:not(:disabled):hover {
    background-color: #138496;
    border-color: #117a8b;
}

.btn-primary:not(:disabled):hover {
    background-color: #0056b3;
    border-color: #004085;
}
</style>

<script>
$(document).ready(function() {
    var selectedMember = null;
    var selectedBooks = [];

    // Member search functionality
    $('#memberSearch').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/searchMembers',
                method: 'POST',
                data: { query: query },
                dataType: 'json',
                success: function(response) {
                    displayMemberResults(response);
                },
                error: function(xhr, status, error) {
                    console.log('Member search error:', error);
                }
            });
        } else {
            $('#memberSearchResults').hide();
        }
    });

    // Book search functionality
    $('#bookSearch').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/searchBooks',
                method: 'POST',
                data: { query: query },
                dataType: 'json',
                success: function(response) {
                    displayBookResults(response);
                },
                error: function(xhr, status, error) {
                    console.log('Book search error:', error);
                }
            });
        } else {
            $('#bookSearchResults').hide();
            $('#addBookBtn').prop('disabled', true);
            $('#issueBookBtn').prop('disabled', true);
        }
    });

    // Display member search results
    function displayMemberResults(members) {
        var html = '';
        if (members.length > 0) {
            members.forEach(function(member) {
                var name = member.member_type === 'student' ? 
                    member.firstname + ' ' + member.lastname : 
                    member.teacher_name;
                
                var details = member.member_type === 'student' ? 
                    'Student - ' + member.admission_no + (member.class_section ? ' - ' + member.class_section : '') :
                    'Staff - ' + member.admission_no;

                html += '<div class="search-result-item" data-member=\'' + JSON.stringify(member) + '\'>';
                html += '<div class="member-info">' + name + '</div>';
                html += '<div class="member-details">' + details + '</div>';
                html += '</div>';
            });
            $('#memberSearchResults').html(html).show();
        } else {
            $('#memberSearchResults').hide();
        }
    }

    // Display book search results
    function displayBookResults(books) {
        var html = '';
        if (books.length > 0) {
            books.forEach(function(book) {
                html += '<div class="search-result-item" data-book=\'' + JSON.stringify(book) + '\'>';
                html += '<div class="book-info">' + book.book_title + '</div>';
                html += '<div class="book-details">Author: ' + (book.author || 'N/A') + ' | Book No: ' + (book.book_no || 'N/A') + '</div>';
                html += '</div>';
            });
            $('#bookSearchResults').html(html).show();
        } else {
            $('#bookSearchResults').hide();
        }
    }

    // Handle member selection
    $(document).on('click', '#memberSearchResults .search-result-item', function() {
        var member = JSON.parse($(this).attr('data-member'));
        selectedMember = member;
        
        var name = member.member_type === 'student' ? 
            member.firstname + ' ' + member.lastname : 
            member.teacher_name;
        
        $('#memberSearch').val(name);
        $('#selectedMemberId').val(member.lib_member_id);
        $('#memberSearchResults').hide();
        
        // Enable book search
        $('#bookSearch').prop('disabled', false);
        $('#returnDate').prop('disabled', false);
        
        // Set default return date (7 days from today)
        var defaultDate = new Date();
        defaultDate.setDate(defaultDate.getDate() + 7);
        $('#returnDate').val(defaultDate.toISOString().split('T')[0]);
    });

    // Handle book selection
    $(document).on('click', '#bookSearchResults .search-result-item', function() {
        var book = JSON.parse($(this).attr('data-book'));
        
        $('#bookSearch').val(book.book_title);
        $('#bookSearch').data('selectedBook', book);
        $('#bookSearchResults').hide();
        
        // Enable both buttons if member is selected
        if (selectedMember) {
            $('#addBookBtn').prop('disabled', false);
            $('#issueBookBtn').prop('disabled', false);
        }
    });

    // Add book to list
    $('#addBookBtn').click(function(e) {
        e.preventDefault();
        
        var selectedBook = $('#bookSearch').data('selectedBook');
        var returnDate = $('#returnDate').val();
        
        if (selectedBook && returnDate) {
            // Check if book already added
            var bookExists = selectedBooks.find(book => book.id === selectedBook.id);
            if (!bookExists) {
                var bookToAdd = {
                    id: selectedBook.id,
                    book_title: selectedBook.book_title,
                    author: selectedBook.author,
                    book_no: selectedBook.book_no,
                    return_date: returnDate
                };
                
                selectedBooks.push(bookToAdd);
                updateSelectedBooksTable();
                
                // Clear book search
                $('#bookSearch').val('');
                $('#bookSearch').removeData('selectedBook');
                $('#bookSearchResults').hide();
                
                // Disable buttons until next book is selected
                $('#addBookBtn').prop('disabled', true);
                $('#issueBookBtn').prop('disabled', true);
            } else {
                showWarningMessage('Book already added to the list!');
            }
        } else {
            showWarningMessage('Please select a book and return date!');
        }
    });

    // Issue single book
    $('#issueBookBtn').click(function() {
        var selectedBook = $('#bookSearch').data('selectedBook');
        var returnDate = $('#returnDate').val();

        if (selectedMember && selectedBook && returnDate) {
            var requestData = {
                member_id: selectedMember.lib_member_id,
                books: [{
                    id: selectedBook.id,
                    book_title: selectedBook.book_title,
                    return_date: returnDate
                }]
            };

            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/issueBooks',
                method: 'POST',
                data: requestData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showSuccessMessage('Book issued successfully!');
                        // Clear book search
                        $('#bookSearch').val('');
                        $('#bookSearch').removeData('selectedBook');
                        $('#bookSearchResults').hide();
                        $('#issueBookBtn').prop('disabled', true);
                        $('#addBookBtn').prop('disabled', true);
                    } else {
                        showErrorMessage('Error: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showErrorMessage('Error issuing book. Please try again.');
                }
            });
        } else {
            showWarningMessage('Please select a member, book and return date!');
        }
    });

    // Issue all books
    $('#issueAllBooksBtn').click(function() {
        if (selectedMember && selectedBooks.length > 0) {
            var requestData = {
                member_id: selectedMember.lib_member_id,
                books: selectedBooks
            };

            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/issueBooks',
                method: 'POST',
                data: requestData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showSuccessMessage('Books issued successfully!');
                        resetQuickIssue();
                    } else {
                        showErrorMessage('Error: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showErrorMessage('Error issuing books. Please try again.');
                }
            });
        } else {
            showWarningMessage('Please select a member and add books to the list!');
        }
    });

    // Reset form
    $('#resetBtn').click(function() {
        resetQuickIssue();
    });

    // Update selected books table
    function updateSelectedBooksTable() {
        var html = '';
        selectedBooks.forEach(function(book, index) {
            html += '<tr>';
            html += '<td>' + book.book_title + '</td>';
            html += '<td>' + (book.author || 'N/A') + '</td>';
            html += '<td>' + (book.book_no || 'N/A') + '</td>';
            html += '<td>' + book.return_date + '</td>';
            html += '<td><button type="button" class="btn btn-xs btn-danger" onclick="removeBook(' + index + ')" title="Remove book"><i class="fa fa-trash"></i></button></td>';
            html += '</tr>';
        });

        $('#selectedBooksTable').html(html);

        if (selectedBooks.length > 0) {
            $('#selectedBooksSection').show();
            $('#issueAllBooksBtn').prop('disabled', false);
        } else {
            $('#selectedBooksSection').hide();
            $('#issueAllBooksBtn').prop('disabled', true);
        }

        // Update button text with count
        if (selectedBooks.length > 0) {
            $('#issueAllBooksBtn').html('<i class="fa fa-check"></i> Issue ' + selectedBooks.length + ' Book' + (selectedBooks.length > 1 ? 's' : ''));
        } else {
            $('#issueAllBooksBtn').html('<i class="fa fa-check"></i> Issue All Books');
        }
    }

    // Remove book from list
    window.removeBook = function(index) {
        selectedBooks.splice(index, 1);
        updateSelectedBooksTable();
    };

    // Reset quick issue form
    function resetQuickIssue() {
        selectedBooks = [];
        selectedMember = null;
        $('#quickIssueForm')[0].reset();
        $('#selectedMemberId').val('');
        $('#bookSearch').prop('disabled', true);
        $('#returnDate').prop('disabled', true);
        $('#addBookBtn').prop('disabled', true);
        $('#issueBookBtn').prop('disabled', true);
        $('#issueAllBooksBtn').prop('disabled', true);
        $('#selectedBooksSection').hide();
        $('#memberSearchResults').hide();
        $('#bookSearchResults').hide();
    }

    // Hide search results when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('#memberSearch, #memberSearchResults').length) {
            $('#memberSearchResults').hide();
        }
        if (!$(e.target).closest('#bookSearch, #bookSearchResults').length) {
            $('#bookSearchResults').hide();
        }
    });

    // Message functions for better user feedback
    function showSuccessMessage(message) {
        showNotification(message, 'success');
    }

    function showErrorMessage(message) {
        showNotification(message, 'danger');
    }

    function showWarningMessage(message) {
        showNotification(message, 'warning');
    }

    function showInfoMessage(message) {
        showNotification(message, 'info');
    }

    function showNotification(message, type) {
        var alertClass = 'alert-' + type;
        var icon = '';

        switch(type) {
            case 'success': icon = '<i class="fa fa-check-circle"></i> '; break;
            case 'danger': icon = '<i class="fa fa-times-circle"></i> '; break;
            case 'warning': icon = '<i class="fa fa-exclamation-triangle"></i> '; break;
            case 'info': icon = '<i class="fa fa-info-circle"></i> '; break;
        }

        $('#notificationMessage')
            .removeClass('alert-success alert-danger alert-warning alert-info')
            .addClass(alertClass);

        $('#notificationText').html(icon + message);

        $('#notificationArea').fadeIn();

        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(function() {
                $('#notificationArea').fadeOut();
            }, 5000);
        }
    }

    function hideNotification() {
        $('#notificationArea').fadeOut();
    }
});
</script>
