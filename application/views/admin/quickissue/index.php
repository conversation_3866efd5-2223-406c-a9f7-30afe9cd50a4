<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-flash"></i> <?php echo $this->lang->line('quick_issue'); ?> <small><?php echo $this->lang->line('student_information'); ?></small>
        </h1>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-flash"></i> <?php echo $this->lang->line('quick_book_issue'); ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-12">
                                <form id="quickIssueForm" class="form-horizontal">
                                    <input type="hidden" id="selectedMemberId" name="member_id" value="">
                                    
                                    <!-- Member Search Row -->
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label"><?php echo $this->lang->line('member'); ?> <span class="req">*</span></label>
                                        <div class="col-sm-4">
                                            <input type="text" id="memberSearch" class="form-control" placeholder="Search student or staff..." autocomplete="off">
                                            <div id="memberSearchResults" class="search-results"></div>
                                        </div>
                                        
                                        <label class="col-sm-2 control-label"><?php echo $this->lang->line('book'); ?> <span class="req">*</span></label>
                                        <div class="col-sm-4">
                                            <div class="input-group">
                                                <input type="text" id="bookSearch" class="form-control" placeholder="Search book..." autocomplete="off" disabled>
                                                <div class="input-group-btn">
                                                    <button type="button" id="addBookBtn" class="btn btn-success" disabled>
                                                        <i class="fa fa-plus"></i>
                                                    </button>
                                                    <button type="button" id="issueBookBtn" class="btn btn-info" disabled>
                                                        <i class="fa fa-flash"></i> Issue
                                                    </button>
                                                </div>
                                            </div>
                                            <div id="bookSearchResults" class="search-results"></div>
                                        </div>
                                    </div>

                                    <!-- Return Date Row -->
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label"><?php echo $this->lang->line('return_date'); ?> <span class="req">*</span></label>
                                        <div class="col-sm-4">
                                            <input type="date" id="returnDate" class="form-control" disabled>
                                        </div>
                                        <div class="col-sm-6">
                                            <button type="button" id="issueAllBooksBtn" class="btn btn-primary" disabled>
                                                <i class="fa fa-check"></i> Issue All Books
                                            </button>
                                            <button type="button" id="resetBtn" class="btn btn-default">
                                                <i class="fa fa-refresh"></i> Reset
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Selected Books Section -->
                        <div id="selectedBooksSection" class="row" style="display: none;">
                            <div class="col-md-12">
                                <h4><i class="fa fa-list"></i> Selected Books</h4>
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Book Title</th>
                                                <th>Author</th>
                                                <th>Book No</th>
                                                <th>Return Date</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="selectedBooksTable">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.search-result-item:hover {
    background-color: #f5f5f5;
}

.search-result-item:last-child {
    border-bottom: none;
}

.member-info {
    font-weight: bold;
    color: #333;
}

.member-details {
    font-size: 0.9em;
    color: #666;
}

.book-info {
    font-weight: bold;
    color: #333;
}

.book-details {
    font-size: 0.9em;
    color: #666;
}

.form-group {
    position: relative;
}
</style>

<script>
$(document).ready(function() {
    var selectedMember = null;
    var selectedBooks = [];

    // Member search functionality
    $('#memberSearch').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/searchMembers',
                method: 'POST',
                data: { query: query },
                dataType: 'json',
                success: function(response) {
                    displayMemberResults(response);
                },
                error: function(xhr, status, error) {
                    console.log('Member search error:', error);
                }
            });
        } else {
            $('#memberSearchResults').hide();
        }
    });

    // Book search functionality
    $('#bookSearch').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/searchBooks',
                method: 'POST',
                data: { query: query },
                dataType: 'json',
                success: function(response) {
                    displayBookResults(response);
                },
                error: function(xhr, status, error) {
                    console.log('Book search error:', error);
                }
            });
        } else {
            $('#bookSearchResults').hide();
            $('#addBookBtn').prop('disabled', true);
            $('#issueBookBtn').prop('disabled', true);
        }
    });

    // Display member search results
    function displayMemberResults(members) {
        var html = '';
        if (members.length > 0) {
            members.forEach(function(member) {
                var name = member.member_type === 'student' ? 
                    member.firstname + ' ' + member.lastname : 
                    member.teacher_name;
                
                var details = member.member_type === 'student' ? 
                    'Student - ' + member.admission_no + (member.class_section ? ' - ' + member.class_section : '') :
                    'Staff - ' + member.admission_no;

                html += '<div class="search-result-item" data-member=\'' + JSON.stringify(member) + '\'>';
                html += '<div class="member-info">' + name + '</div>';
                html += '<div class="member-details">' + details + '</div>';
                html += '</div>';
            });
            $('#memberSearchResults').html(html).show();
        } else {
            $('#memberSearchResults').hide();
        }
    }

    // Display book search results
    function displayBookResults(books) {
        var html = '';
        if (books.length > 0) {
            books.forEach(function(book) {
                html += '<div class="search-result-item" data-book=\'' + JSON.stringify(book) + '\'>';
                html += '<div class="book-info">' + book.book_title + '</div>';
                html += '<div class="book-details">Author: ' + (book.author || 'N/A') + ' | Book No: ' + (book.book_no || 'N/A') + '</div>';
                html += '</div>';
            });
            $('#bookSearchResults').html(html).show();
        } else {
            $('#bookSearchResults').hide();
        }
    }

    // Handle member selection
    $(document).on('click', '#memberSearchResults .search-result-item', function() {
        var member = JSON.parse($(this).attr('data-member'));
        selectedMember = member;
        
        var name = member.member_type === 'student' ? 
            member.firstname + ' ' + member.lastname : 
            member.teacher_name;
        
        $('#memberSearch').val(name);
        $('#selectedMemberId').val(member.lib_member_id);
        $('#memberSearchResults').hide();
        
        // Enable book search
        $('#bookSearch').prop('disabled', false);
        $('#returnDate').prop('disabled', false);
        
        // Set default return date (7 days from today)
        var defaultDate = new Date();
        defaultDate.setDate(defaultDate.getDate() + 7);
        $('#returnDate').val(defaultDate.toISOString().split('T')[0]);
    });

    // Handle book selection
    $(document).on('click', '#bookSearchResults .search-result-item', function() {
        var book = JSON.parse($(this).attr('data-book'));
        
        $('#bookSearch').val(book.book_title);
        $('#bookSearch').data('selectedBook', book);
        $('#bookSearchResults').hide();
        
        // Enable both buttons if member is selected
        if (selectedMember) {
            $('#addBookBtn').prop('disabled', false);
            $('#issueBookBtn').prop('disabled', false);
        }
    });

    // Add book to list
    $('#addBookBtn').click(function(e) {
        e.preventDefault();
        
        var selectedBook = $('#bookSearch').data('selectedBook');
        var returnDate = $('#returnDate').val();
        
        if (selectedBook && returnDate) {
            // Check if book already added
            var bookExists = selectedBooks.find(book => book.id === selectedBook.id);
            if (!bookExists) {
                var bookToAdd = {
                    id: selectedBook.id,
                    book_title: selectedBook.book_title,
                    author: selectedBook.author,
                    book_no: selectedBook.book_no,
                    return_date: returnDate
                };
                
                selectedBooks.push(bookToAdd);
                updateSelectedBooksTable();
                
                // Clear book search
                $('#bookSearch').val('');
                $('#bookSearch').removeData('selectedBook');
                $('#bookSearchResults').hide();
                
                // Disable buttons until next book is selected
                $('#addBookBtn').prop('disabled', true);
                $('#issueBookBtn').prop('disabled', true);
            } else {
                alert('Book already added to the list!');
            }
        } else {
            alert('Please select a book and return date!');
        }
    });

    // Issue single book
    $('#issueBookBtn').click(function() {
        var selectedBook = $('#bookSearch').data('selectedBook');
        var returnDate = $('#returnDate').val();

        if (selectedMember && selectedBook && returnDate) {
            var requestData = {
                member_id: selectedMember.lib_member_id,
                books: [{
                    id: selectedBook.id,
                    book_title: selectedBook.book_title,
                    return_date: returnDate
                }]
            };

            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/issueBooks',
                method: 'POST',
                data: requestData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('Book issued successfully!');
                        // Clear book search
                        $('#bookSearch').val('');
                        $('#bookSearch').removeData('selectedBook');
                        $('#bookSearchResults').hide();
                        $('#issueBookBtn').prop('disabled', true);
                        $('#addBookBtn').prop('disabled', true);
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error issuing book. Please try again.');
                }
            });
        } else {
            alert('Please select a member, book and return date!');
        }
    });

    // Issue all books
    $('#issueAllBooksBtn').click(function() {
        if (selectedMember && selectedBooks.length > 0) {
            var requestData = {
                member_id: selectedMember.lib_member_id,
                books: selectedBooks
            };

            $.ajax({
                url: '<?php echo base_url(); ?>admin/quickissue/issueBooks',
                method: 'POST',
                data: requestData,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('Books issued successfully!');
                        resetQuickIssue();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error issuing books. Please try again.');
                }
            });
        } else {
            alert('Please select a member and add books to the list!');
        }
    });

    // Reset form
    $('#resetBtn').click(function() {
        resetQuickIssue();
    });

    // Update selected books table
    function updateSelectedBooksTable() {
        var html = '';
        selectedBooks.forEach(function(book, index) {
            html += '<tr>';
            html += '<td>' + book.book_title + '</td>';
            html += '<td>' + (book.author || 'N/A') + '</td>';
            html += '<td>' + (book.book_no || 'N/A') + '</td>';
            html += '<td>' + book.return_date + '</td>';
            html += '<td><button type="button" class="btn btn-sm btn-danger" onclick="removeBook(' + index + ')"><i class="fa fa-trash"></i></button></td>';
            html += '</tr>';
        });

        $('#selectedBooksTable').html(html);

        if (selectedBooks.length > 0) {
            $('#selectedBooksSection').show();
            $('#issueAllBooksBtn').prop('disabled', false);
        } else {
            $('#selectedBooksSection').hide();
            $('#issueAllBooksBtn').prop('disabled', true);
        }
    }

    // Remove book from list
    window.removeBook = function(index) {
        selectedBooks.splice(index, 1);
        updateSelectedBooksTable();
    };

    // Reset quick issue form
    function resetQuickIssue() {
        selectedBooks = [];
        selectedMember = null;
        $('#quickIssueForm')[0].reset();
        $('#selectedMemberId').val('');
        $('#bookSearch').prop('disabled', true);
        $('#returnDate').prop('disabled', true);
        $('#addBookBtn').prop('disabled', true);
        $('#issueBookBtn').prop('disabled', true);
        $('#issueAllBooksBtn').prop('disabled', true);
        $('#selectedBooksSection').hide();
        $('#memberSearchResults').hide();
        $('#bookSearchResults').hide();
    }

    // Hide search results when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('#memberSearch, #memberSearchResults').length) {
            $('#memberSearchResults').hide();
        }
        if (!$(e.target).closest('#bookSearch, #bookSearchResults').length) {
            $('#bookSearchResults').hide();
        }
    });
});
</script>
