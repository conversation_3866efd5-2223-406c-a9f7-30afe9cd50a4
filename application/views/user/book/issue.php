<?php
$currency_symbol = $this->customlib->getSchoolCurrencyFormat();
?>
<div class="content-wrapper">
    <section class="content-header">
        <h1><i class="fa fa-book"></i> <?php //echo $this->lang->line('library_book'); ?> <small></small></h1>
    </section>
    <!-- Main content -->
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary"><div class="box-header ptbnull">
                        <h3 class="box-title titlefix"> <?php echo $this->lang->line('book_issued'); ?></h3>
                        <div class="box-tools pull-right">
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive mailbox-messages">
                            <div class="download_label"><?php echo $this->lang->line('book_issued'); ?></div>
                            <table class="table table-striped table-bordered table-hover example">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('book_title'); ?></th>
                                        <th><?php echo $this->lang->line('book_number'); ?></th>
                                        <th><?php echo $this->lang->line('author'); ?></th>
                                        <th><?php echo $this->lang->line('issue_date'); ?></th>
                                        <th><?php echo $this->lang->line('due_return_date'); ?></th>
                                        <th ><?php echo $this->lang->line('return_date'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($isCheck == 0) {
    ?>
                                        <?php
} else {
    if (isset($bookList)) {
        ?>
                                            <?php if (empty($bookList)) {
            ?>

                                                <?php
} else {
            $count = 1;
            foreach ($bookList as $book) {
                $cls = "";
                if ($book['is_returned'] == 1) {
                    $cls = "success";
                }
                ?>
                                                    <tr class="<?php echo $cls; ?>">
                                                        <td class="mailbox-name">
                                                            <a href="javascript:void(0)" class="book-details-link" data-book-id="<?php echo $book['book_id']; ?>" data-isbn="<?php echo $book['isbn_no']; ?>">
                                                                <?php echo $book['book_title']; ?>
                                                            </a>
                                                        </td>
                                                        <td class="mailbox-name"> <?php echo $book['book_no'] ?></td>
                                                        <td class="mailbox-name"> <?php echo $book['author'] ?></td>
                                                        <td class="mailbox-name">
                                                            <?php
if ($book['issue_date'] != '') {
                    echo date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($book['issue_date']));
                }
                ?>
                                                        </td>
                                                        <td >
                                                            <?php
if ($book['duereturn_date'] != '') {
                    echo date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($book['duereturn_date']));
                }
                ?>
                                                        </td>
                                                        <td >
                                                            <?php
if ($book['return_date'] != '') {
                    echo date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($book['return_date']));
                }
                ?>
                                                        </td>
                                                    </tr>
                                                    <?php
}
            $count++;
        }
        ?>
                                            <?php
}
}
?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
            </div>
        </div>
    </section>
</div>

<!-- Book Details Modal -->
<div class="modal fade" id="bookDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookDetailsModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="bookDetailsModalLabel"><?php echo $this->lang->line('book'); ?> <?php echo $this->lang->line('details'); ?></h4>
            </div>
            <div class="modal-body">
                <div id="bookDetailsContent">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin fa-2x"></i>
                        <p><?php echo $this->lang->line('please_wait'); ?>...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo $this->lang->line('close'); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for Book Details Modal -->
<style>
.book-details-link {
    color: #337ab7;
    text-decoration: none;
    cursor: pointer;
}
.book-details-link:hover {
    color: #23527c;
    text-decoration: underline;
}
.modal-lg {
    width: 90%;
    max-width: 1000px;
}
#bookDetailsModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}
#bookDetailsModal .table td {
    padding: 8px;
    border-top: 1px solid #ddd;
}
#bookDetailsModal .table td:first-child {
    width: 200px;
    font-weight: bold;
}
#bookDetailsModal .well {
    max-height: 200px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    padding: 15px;
}
</style>

<script>
    var base_url = '<?php echo base_url() ?>';

    $(document).ready(function () {
        $('.detail_popover').popover({
            placement: 'right',
            trigger: 'hover',
            container: 'body',
            html: true,
            content: function () {
                return $(this).closest('td').find('.fee_detail_popover').html();
            }
        });

        // Handle book details popup
        $(document).on('click', '.book-details-link', function(e) {
            e.preventDefault();
            var bookId = $(this).data('book-id');
            var isbn = $(this).data('isbn');

            $('#bookDetailsModal').modal('show');
            loadBookDetails(bookId, isbn);
        });
    });

    function loadBookDetails(bookId, isbn) {
        // Show loading
        $('#bookDetailsContent').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><p><?php echo $this->lang->line('please_wait'); ?>...</p></div>');

        // Fetch book details from database
        $.ajax({
            url: base_url + 'user/book/getBookDetails/' + bookId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    var book = response.data;

                    // Fetch Google Books data if ISBN exists
                    if (isbn && isbn.trim() !== '') {
                        fetchGoogleBookInfo(book, isbn);
                    } else {
                        displayBookDetails(book, null);
                    }
                } else {
                    $('#bookDetailsContent').html('<div class="alert alert-danger">Error loading book details</div>');
                }
            },
            error: function() {
                $('#bookDetailsContent').html('<div class="alert alert-danger">Error loading book details</div>');
            }
        });
    }

    function fetchGoogleBookInfo(book, isbn) {
        console.log('Fetching Google Books info for ISBN:', isbn);

        $.ajax({
            url: base_url + 'user/book/getGoogleBookInfo',
            type: 'POST',
            data: { isbn: isbn },
            dataType: 'json',
            success: function(response) {
                console.log('Google Books API response:', response);
                var googleData = null;
                if (response.status === 'success') {
                    googleData = response.data;
                    console.log('Google Books data found:', googleData);
                } else {
                    console.log('Google Books API error:', response.message);
                }
                displayBookDetails(book, googleData);
            },
            error: function(xhr, status, error) {
                console.log('AJAX error fetching Google Books data:', error);
                console.log('Response text:', xhr.responseText);
                displayBookDetails(book, null);
            }
        });
    }

    function displayBookDetails(book, googleData) {
        var html = '<div class="row">';

        // Left column - Book cover
        html += '<div class="col-md-4">';

        var hasImage = false;
        var imageUrl = '';

        // Try to get the best available image
        if (googleData) {
            if (googleData.thumbnail && googleData.thumbnail.trim() !== '') {
                imageUrl = googleData.thumbnail;
                hasImage = true;
            } else if (googleData.smallThumbnail && googleData.smallThumbnail.trim() !== '') {
                imageUrl = googleData.smallThumbnail;
                hasImage = true;
            }
        }

        if (hasImage) {
            html += '<div class="text-center">';
            html += '<img id="bookCoverImg" src="' + imageUrl + '" class="img-responsive center-block" style="max-height: 300px; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" alt="Book Cover" onload="console.log(\'Image loaded successfully: \' + this.src)" onerror="handleImageError(this, \'' + (googleData.smallThumbnail || '') + '\')">';
            html += '<div id="imagePlaceholder" class="book-cover-placeholder" style="display: none; background: #f5f5f5; padding: 50px; border: 2px dashed #ddd; border-radius: 4px;">';
            html += '<i class="fa fa-book fa-5x text-muted"></i>';
            html += '<p class="text-muted">Cover Not Available</p>';
            html += '</div>';
            html += '</div>';
        } else {
            html += '<div class="text-center book-cover-placeholder" style="background: #f5f5f5; padding: 50px; border: 2px dashed #ddd; border-radius: 4px;">';
            html += '<i class="fa fa-book fa-5x text-muted"></i>';
            html += '<p class="text-muted">No Cover Image</p>';
            html += '</div>';
        }
        html += '</div>';

        // Right column - Book details
        html += '<div class="col-md-8">';
        html += '<h3>' + book.book_title + '</h3>';

        // Basic Information
        html += '<table class="table table-striped">';
        html += '<tr><td><strong><?php echo $this->lang->line('book_number'); ?>:</strong></td><td>' + (book.book_no || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('isbn_number'); ?>:</strong></td><td>' + (book.isbn_no || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('author'); ?>:</strong></td><td>' + (book.author || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('publisher'); ?>:</strong></td><td>' + (book.publish || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('subject'); ?>:</strong></td><td>' + (book.subject || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_type'); ?>:</strong></td><td>' + (book.book_type || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_category'); ?>:</strong></td><td>' + (book.book_category || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_collection'); ?>:</strong></td><td>' + (book.book_collection || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_language'); ?>:</strong></td><td>' + (book.book_language || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('rack_number'); ?>:</strong></td><td>' + (book.rack_no || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('shelving_location'); ?>:</strong></td><td>' + (book.shelving_location || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('source_of_classification'); ?>:</strong></td><td>' + (book.source_of_classification || 'N/A') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('qty'); ?>:</strong></td><td>' + (book.qty || '0') + '</td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('book_price'); ?>:</strong></td><td>' + (book.perunitcost || '0') + '</td></tr>';

        // Status with color coding
        var statusClass = 'label-default';
        switch(book.lost_status) {
            case 'Available': statusClass = 'label-success'; break;
            case 'Lost': statusClass = 'label-danger'; break;
            case 'Damaged': statusClass = 'label-warning'; break;
            case 'Missing': statusClass = 'label-danger'; break;
            case 'Under Repair': statusClass = 'label-info'; break;
        }
        html += '<tr><td><strong><?php echo $this->lang->line('lost_status'); ?>:</strong></td><td><span class="label ' + statusClass + '">' + (book.lost_status || 'Available') + '</span></td></tr>';
        html += '<tr><td><strong><?php echo $this->lang->line('post_date'); ?>:</strong></td><td>' + (book.postdate || 'N/A') + '</td></tr>';
        html += '</table>';

        html += '</div>';
        html += '</div>';

        // Description section
        html += '<div class="row" style="margin-top: 20px;">';
        html += '<div class="col-md-12">';
        html += '<h4><?php echo $this->lang->line('description'); ?></h4>';

        var description = '';
        var descriptionSource = '';

        // Prioritize Google Books description
        if (googleData && googleData.description && googleData.description.trim() !== '') {
            description = googleData.description;
            descriptionSource = '<small class="text-muted"><i class="fa fa-google"></i> Description from Google Books</small>';
        } else if (book.description && book.description.trim() !== '') {
            description = book.description;
            descriptionSource = '<small class="text-muted"><i class="fa fa-database"></i> Local description</small>';
        } else {
            description = '<?php echo $this->lang->line('no_description'); ?>';
            descriptionSource = '';
        }

        html += '<div class="well" style="max-height: 300px; overflow-y: auto;">' + description + '</div>';
        if (descriptionSource) {
            html += descriptionSource;
        }
        html += '</div>';
        html += '</div>';

        // Google Books Preview Link
        if (googleData && googleData.previewLink) {
            html += '<div class="row" style="margin-top: 20px;">';
            html += '<div class="col-md-12">';
            html += '<a href="' + googleData.previewLink + '" target="_blank" class="btn btn-primary"><i class="fa fa-google"></i> Preview on Google Books</a>';
            html += '</div>';
            html += '</div>';
        }

        // Google Books additional info
        if (googleData) {
            html += '<div class="row" style="margin-top: 20px;">';
            html += '<div class="col-md-12">';
            html += '<h4>Additional Information from Google Books</h4>';
            html += '<table class="table table-striped">';
            if (googleData.publishedDate) {
                html += '<tr><td><strong>Published Date:</strong></td><td>' + googleData.publishedDate + '</td></tr>';
            }
            if (googleData.pageCount) {
                html += '<tr><td><strong>Page Count:</strong></td><td>' + googleData.pageCount + '</td></tr>';
            }
            if (googleData.categories) {
                html += '<tr><td><strong>Categories:</strong></td><td>' + googleData.categories + '</td></tr>';
            }
            if (googleData.language) {
                html += '<tr><td><strong>Language:</strong></td><td>' + googleData.language.toUpperCase() + '</td></tr>';
            }
            html += '</table>';
            html += '</div>';
            html += '</div>';
        }

        $('#bookDetailsContent').html(html);
    }

    function handleImageError(img, fallbackUrl) {
        console.log('Image failed to load:', img.src);

        // Try fallback image if available
        if (fallbackUrl && fallbackUrl.trim() !== '' && img.src !== fallbackUrl) {
            console.log('Trying fallback image:', fallbackUrl);
            img.src = fallbackUrl;
            return;
        }

        // If no fallback or fallback also failed, show placeholder
        console.log('No fallback available, showing placeholder');
        img.style.display = 'none';
        var placeholder = document.getElementById('imagePlaceholder');
        if (placeholder) {
            placeholder.style.display = 'block';
        }
    }
</script>