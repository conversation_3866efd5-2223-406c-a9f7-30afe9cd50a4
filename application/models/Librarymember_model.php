<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * This funtion takes id as a parameter and will fetch the record.
     * If id is not provided, then it will fetch all the records form the table.
     * @param int $id
     * @return mixed
     */
    public function get()
    {
        try {
            $result = array();

            // Get students with class and section information
            $students_query = $this->db->query("
                SELECT
                    students.id as lib_member_id,
                    students.id as stu_id,
                    students.admission_no,
                    students.firstname,
                    students.lastname,
                    students.middlename,
                    students.guardian_phone,
                    'student' as member_type,
                    classes.class,
                    sections.section,
                    NULL as teacher_name,
                    NULL as teacher_email,
                    NULL as teacher_sex,
                    NULL as teacher_phone,
                    NULL as staff_id,
                    NULL as emp_id
                FROM students
                LEFT JOIN student_session ON student_session.student_id = students.id
                LEFT JOIN classes ON student_session.class_id = classes.id
                LEFT JOIN sections ON student_session.section_id = sections.id
                WHERE students.is_active = 'yes'
                ORDER BY students.admission_no ASC
            ");

            $students = $students_query->result_array();

            // Add library card numbers and class_section for students
            foreach ($students as &$student) {
                $admission = $student['admission_no'] ? $student['admission_no'] : 'STU-' . $student['lib_member_id'];
                $student['library_card_no'] = 'LIB-' . $admission;
                $student['admission_no'] = $admission;

                // Format class and section
                if ($student['class'] && $student['section']) {
                    $student['class_section'] = $student['class'] . ' (' . $student['section'] . ')';
                } else {
                    $student['class_section'] = '';
                }
            }

            // Use built-in Staff model to get staff members safely
            $this->load->model('staff_model');
            $staff_data = $this->staff_model->get();

            // Process staff data to match our format
            $staff = array();
            foreach ($staff_data as $staff_member) {
                $emp_id = $staff_member['employee_id'] ? $staff_member['employee_id'] : 'STAFF-' . $staff_member['id'];

                $staff[] = array(
                    'lib_member_id' => $staff_member['id'],
                    'staff_id' => $staff_member['id'],
                    'admission_no' => $emp_id,
                    'firstname' => null,
                    'lastname' => null,
                    'middlename' => null,
                    'guardian_phone' => $staff_member['contact_no'],
                    'member_type' => 'staff',
                    'class_section' => '', // Empty for staff
                    'teacher_name' => trim($staff_member['name'] . ' ' . $staff_member['surname']),
                    'teacher_email' => $staff_member['email'],
                    'teacher_sex' => $staff_member['sex'],
                    'teacher_phone' => $staff_member['contact_no'],
                    'stu_id' => null,
                    'emp_id' => $emp_id,
                    'library_card_no' => 'LIB-' . $emp_id
                );
            }

            // Combine students and staff
            $result = array_merge($students, $staff);

            return $result;

        } catch (Exception $e) {
            // Log error and return test data as fallback
            log_message('error', 'Library member model error: ' . $e->getMessage());

            // Return test data as fallback
            return array(
                array(
                    'lib_member_id' => 999,
                    'stu_id' => 999,
                    'admission_no' => 'ERROR999',
                    'firstname' => 'Database',
                    'lastname' => 'Error',
                    'middlename' => '',
                    'guardian_phone' => '0000000000',
                    'member_type' => 'student',
                    'teacher_name' => null,
                    'teacher_email' => null,
                    'teacher_sex' => null,
                    'teacher_phone' => null,
                    'staff_id' => null,
                    'emp_id' => null,
                    'library_card_no' => 'LIB-ERROR999'
                )
            );
        }
    }



    public function checkIsMember($member_type, $id)
    {
        try {
            // For now, just return empty array to get the page working
            // This will be enhanced later
            return array();
        } catch (Exception $e) {
            log_message('error', 'checkIsMember error: ' . $e->getMessage());
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }

        try {
            // First try to find as student
            $student = $this->getStudentData($id);
            if ($student) {
                return $student;
            }

            // Then try to find as staff
            $staff = $this->getTeacherData($id);
            if ($staff) {
                return $staff;
            }

            return null;
        } catch (Exception $e) {
            log_message('error', 'getByMemberID error: ' . $e->getMessage());
            return null;
        }
    }

    public function getTeacherData($id)
    {
        try {
            $this->db->select('
                staff.id as lib_member_id,
                "staff" as member_type,
                staff.*
            ');
            $this->db->from('staff');
            $this->db->where('staff.id', $id);
            $this->db->where('staff.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->employee_id ? $result->employee_id : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getTeacherData error: ' . $e->getMessage());
            return null;
        }
    }

    public function getStudentData($id)
    {
        try {
            $this->db->select('
                students.id as lib_member_id,
                "student" as member_type,
                students.*,
                sessions.session as session_year
            ');
            $this->db->from('students');
            $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
            $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
            $this->db->where('students.id', $id);
            $this->db->where('students.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->admission_no ? $result->admission_no : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getStudentData error: ' . $e->getMessage());
            return null;
        }
    }

    public function surrender($id)
    {
        // Simplified version for now - just return true
        // This will be enhanced later once the basic system is working
        return true;
    }

}
