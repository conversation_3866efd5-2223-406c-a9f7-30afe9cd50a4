<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends MY_Model
{
    protected $current_session;

    public function __construct()
    {
        parent::__construct();
        $this->current_session = $this->setting_model->getCurrentSession();
    }

    /**
     * This funtion takes id as a parameter and will fetch the record.
     * If id is not provided, then it will fetch all the records form the table.
     * @param int $id
     * @return mixed
     */
    public function get()
    {
        try {
            // Use the same logic as Student_model->get() method
            $this->db->select('students.id as lib_member_id, students.id as stu_id, students.admission_no, students.firstname, students.lastname, students.middlename, students.guardian_phone, "student" as member_type, NULL as teacher_name, NULL as teacher_email, NULL as teacher_sex, NULL as teacher_phone, NULL as staff_id, NULL as emp_id, classes.class, sections.section');
            $this->db->from('students');
            $this->db->join('student_session', 'student_session.student_id = students.id');
            $this->db->join('classes', 'student_session.class_id = classes.id');
            $this->db->join('sections', 'sections.id = student_session.section_id');
            $this->db->where('student_session.session_id', $this->current_session);
            $this->db->where('students.is_active', 'yes');
            $this->db->order_by('students.admission_no', 'asc');

            $query = $this->db->get();
            $students = $query->result_array();

            // Add library card numbers for students
            foreach ($students as &$student) {
                $admission = $student['admission_no'] ? $student['admission_no'] : 'STU-' . $student['lib_member_id'];
                $student['library_card_no'] = 'LIB-' . $admission;
                $student['admission_no'] = $admission;
            }

            return $students;

        } catch (Exception $e) {
            // Log error
            log_message('error', 'Library member model error: ' . $e->getMessage());

            // Return empty array to prevent 500 error
            return array();
        }
    }



    public function checkIsMember($member_type, $id)
    {
        try {
            // For now, just return empty array to get the page working
            // This will be enhanced later
            return array();
        } catch (Exception $e) {
            log_message('error', 'checkIsMember error: ' . $e->getMessage());
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }

        try {
            // First try to find as student
            $student = $this->getStudentData($id);
            if ($student) {
                return $student;
            }

            // Then try to find as staff
            $staff = $this->getTeacherData($id);
            if ($staff) {
                return $staff;
            }

            return null;
        } catch (Exception $e) {
            log_message('error', 'getByMemberID error: ' . $e->getMessage());
            return null;
        }
    }

    public function getTeacherData($id)
    {
        try {
            $this->db->select('
                staff.id as lib_member_id,
                "staff" as member_type,
                staff.*
            ');
            $this->db->from('staff');
            $this->db->where('staff.id', $id);
            $this->db->where('staff.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->employee_id ? $result->employee_id : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getTeacherData error: ' . $e->getMessage());
            return null;
        }
    }

    public function getStudentData($id)
    {
        try {
            $this->db->select('
                students.id as lib_member_id,
                "student" as member_type,
                students.*,
                sessions.session as session_year
            ');
            $this->db->from('students');
            $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
            $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
            $this->db->where('students.id', $id);
            $this->db->where('students.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->admission_no ? $result->admission_no : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getStudentData error: ' . $e->getMessage());
            return null;
        }
    }

    public function surrender($id)
    {
        // Simplified version for now - just return true
        // This will be enhanced later once the basic system is working
        return true;
    }

}
