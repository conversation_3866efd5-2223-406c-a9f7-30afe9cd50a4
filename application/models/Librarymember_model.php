<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends MY_Model
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * This funtion takes id as a parameter and will fetch the record.
     * If id is not provided, then it will fetch all the records form the table.
     * @param int $id
     * @return mixed
     */
    public function get()
    {
        try {
            // First, let's check if students table exists and has data
            $student_count = $this->db->query("SELECT COUNT(*) as count FROM students")->row()->count;

            if ($student_count == 0) {
                // No students found, return empty array
                return array();
            }

            // Get ALL students with class and section information
            $query = "
                SELECT
                    students.id as lib_member_id,
                    students.id as stu_id,
                    COALESCE(students.admission_no, CONCAT('STU-', students.id)) as admission_no,
                    COALESCE(students.firstname, 'Student') as firstname,
                    COALESCE(students.lastname, CONCAT('User ', students.id)) as lastname,
                    COALESCE(students.middlename, '') as middlename,
                    COALESCE(students.guardian_phone, '') as guardian_phone,
                    'student' as member_type,
                    <PERSON>ULL as teacher_name,
                    NULL as teacher_email,
                    <PERSON><PERSON><PERSON> as teacher_sex,
                    NULL as teacher_phone,
                    NULL as staff_id,
                    NULL as emp_id,
                    COALESCE(classes.class, '') as class_name,
                    COALESCE(sections.section, '') as section_name,
                    CONCAT(COALESCE(classes.class, ''), ' - ', COALESCE(sections.section, '')) as class_section
                FROM students
                LEFT JOIN classes ON students.class_id = classes.id
                LEFT JOIN sections ON students.section_id = sections.id
                ORDER BY students.admission_no, students.id
            ";

            $students = $this->db->query($query)->result_array();

            // Add library card numbers for students
            foreach ($students as &$student) {
                $student['library_card_no'] = 'LIB-' . $student['admission_no'];
            }

            // Now get staff members
            $staff_query = "
                SELECT
                    staff.id as lib_member_id,
                    COALESCE(staff.employee_id, CONCAT('EMP-', staff.id)) as admission_no,
                    COALESCE(staff.name, 'Staff') as firstname,
                    COALESCE(staff.surname, CONCAT('Member ', staff.id)) as lastname,
                    '' as middlename,
                    COALESCE(staff.contact_no, '') as guardian_phone,
                    'staff' as member_type,
                    CONCAT(COALESCE(staff.name, ''), ' ', COALESCE(staff.surname, '')) as teacher_name,
                    staff.email as teacher_email,
                    staff.sex as teacher_sex,
                    staff.contact_no as teacher_phone,
                    staff.id as staff_id,
                    COALESCE(staff.employee_id, CONCAT('EMP-', staff.id)) as emp_id,
                    NULL as stu_id,
                    'Staff' as class_name,
                    COALESCE(staff.designation, 'Employee') as section_name,
                    CONCAT('Staff - ', COALESCE(staff.designation, 'Employee')) as class_section
                FROM staff
                ORDER BY staff.employee_id, staff.id
            ";

            $staff = $this->db->query($staff_query)->result_array();

            // Add library card numbers for staff
            foreach ($staff as &$staff_member) {
                $staff_member['library_card_no'] = 'LIB-' . $staff_member['admission_no'];
            }

            // Combine students and staff
            $result = array_merge($students, $staff);

            return $result;

        } catch (Exception $e) {
            // Log error
            log_message('error', 'Library member model error: ' . $e->getMessage());

            // Return a test record so we can see if the page works
            return array(
                array(
                    'lib_member_id' => 999,
                    'stu_id' => 999,
                    'admission_no' => 'TEST999',
                    'firstname' => 'Test',
                    'lastname' => 'Student',
                    'middlename' => '',
                    'guardian_phone' => '1234567890',
                    'member_type' => 'student',
                    'teacher_name' => null,
                    'teacher_email' => null,
                    'teacher_sex' => null,
                    'teacher_phone' => null,
                    'staff_id' => null,
                    'emp_id' => null,
                    'library_card_no' => 'LIB-TEST999',
                    'class_name' => 'Test Class',
                    'section_name' => 'A',
                    'class_section' => 'Test Class - A'
                )
            );
        }
    }



    public function checkIsMember($member_type, $id)
    {
        try {
            // For now, just return empty array to get the page working
            // This will be enhanced later
            return array();
        } catch (Exception $e) {
            log_message('error', 'checkIsMember error: ' . $e->getMessage());
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }

        try {
            // First try to find as student
            $student = $this->getStudentData($id);
            if ($student) {
                return $student;
            }

            // Then try to find as staff
            $staff = $this->getTeacherData($id);
            if ($staff) {
                return $staff;
            }

            return null;
        } catch (Exception $e) {
            log_message('error', 'getByMemberID error: ' . $e->getMessage());
            return null;
        }
    }

    public function getTeacherData($id)
    {
        try {
            $this->db->select('
                staff.id as lib_member_id,
                "staff" as member_type,
                staff.*
            ');
            $this->db->from('staff');
            $this->db->where('staff.id', $id);
            $this->db->where('staff.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->employee_id ? $result->employee_id : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getTeacherData error: ' . $e->getMessage());
            return null;
        }
    }

    public function getStudentData($id)
    {
        try {
            $this->db->select('
                students.id as lib_member_id,
                "student" as member_type,
                students.*,
                sessions.session as session_year
            ');
            $this->db->from('students');
            $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
            $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
            $this->db->where('students.id', $id);
            $this->db->where('students.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->admission_no ? $result->admission_no : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getStudentData error: ' . $e->getMessage());
            return null;
        }
    }

    public function surrender($id)
    {
        // Simplified version for now - just return true
        // This will be enhanced later once the basic system is working
        return true;
    }

}
