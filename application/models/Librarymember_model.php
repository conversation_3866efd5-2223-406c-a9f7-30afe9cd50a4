<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends MY_Model
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * This funtion takes id as a parameter and will fetch the record.
     * If id is not provided, then it will fetch all the records form the table.
     * @param int $id
     * @return mixed
     */
    public function get()
    {
        try {
            $result = array();

            // Get students with very simple query
            try {
                $students = $this->db->select('
                    id as lib_member_id,
                    id as stu_id,
                    admission_no,
                    firstname,
                    lastname,
                    middlename,
                    guardian_phone
                ')->from('students')
                ->get()->result_array();

                // Process students
                foreach ($students as &$student) {
                    $student['member_type'] = 'student';
                    $student['teacher_name'] = null;
                    $student['teacher_email'] = null;
                    $student['teacher_sex'] = null;
                    $student['teacher_phone'] = null;
                    $student['staff_id'] = null;
                    $student['emp_id'] = null;
                    $student['class_section'] = 'Student';
                    $student['library_card_no'] = 'LIB-' . ($student['admission_no'] ? $student['admission_no'] : $student['lib_member_id']);
                }

                $result = array_merge($result, $students);
            } catch (Exception $e) {
                log_message('error', 'Students query failed: ' . $e->getMessage());
            }

            // Get staff with very simple query
            try {
                $staff = $this->db->select('
                    id as lib_member_id,
                    employee_id,
                    name,
                    surname,
                    contact_no,
                    email,
                    sex
                ')->from('staff')
                ->get()->result_array();

                // Process staff
                foreach ($staff as &$staff_member) {
                    $staff_member['stu_id'] = null;
                    $staff_member['admission_no'] = $staff_member['employee_id'] ? $staff_member['employee_id'] : $staff_member['lib_member_id'];
                    $staff_member['firstname'] = $staff_member['name'];
                    $staff_member['lastname'] = $staff_member['surname'];
                    $staff_member['middlename'] = '';
                    $staff_member['guardian_phone'] = $staff_member['contact_no'];
                    $staff_member['member_type'] = 'staff';
                    $staff_member['teacher_name'] = trim($staff_member['name'] . ' ' . $staff_member['surname']);
                    $staff_member['teacher_email'] = $staff_member['email'];
                    $staff_member['teacher_sex'] = $staff_member['sex'];
                    $staff_member['teacher_phone'] = $staff_member['contact_no'];
                    $staff_member['staff_id'] = $staff_member['lib_member_id'];
                    $staff_member['emp_id'] = $staff_member['employee_id'];
                    $staff_member['class_section'] = 'Staff';
                    $staff_member['library_card_no'] = 'LIB-' . $staff_member['admission_no'];
                }

                $result = array_merge($result, $staff);
            } catch (Exception $e) {
                log_message('error', 'Staff query failed: ' . $e->getMessage());
            }

            return $result;

        } catch (Exception $e) {
            // Log error
            log_message('error', 'Library member model error: ' . $e->getMessage());

            // Return a test record so we can see if the page works
            return array(
                array(
                    'lib_member_id' => 999,
                    'stu_id' => 999,
                    'admission_no' => 'TEST999',
                    'firstname' => 'Test',
                    'lastname' => 'Student',
                    'middlename' => '',
                    'guardian_phone' => '1234567890',
                    'member_type' => 'student',
                    'teacher_name' => null,
                    'teacher_email' => null,
                    'teacher_sex' => null,
                    'teacher_phone' => null,
                    'staff_id' => null,
                    'emp_id' => null,
                    'library_card_no' => 'LIB-TEST999',
                    'class_section' => 'Test Class'
                )
            );
        }
    }



    public function checkIsMember($member_type, $id)
    {
        try {
            // For now, just return empty array to get the page working
            // This will be enhanced later
            return array();
        } catch (Exception $e) {
            log_message('error', 'checkIsMember error: ' . $e->getMessage());
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }

        try {
            // First try to find as student
            $student = $this->getStudentData($id);
            if ($student) {
                return $student;
            }

            // Then try to find as staff
            $staff = $this->getTeacherData($id);
            if ($staff) {
                return $staff;
            }

            return null;
        } catch (Exception $e) {
            log_message('error', 'getByMemberID error: ' . $e->getMessage());
            return null;
        }
    }

    public function getTeacherData($id)
    {
        try {
            $this->db->select('
                staff.id as lib_member_id,
                "staff" as member_type,
                staff.*
            ');
            $this->db->from('staff');
            $this->db->where('staff.id', $id);
            $this->db->where('staff.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->employee_id ? $result->employee_id : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getTeacherData error: ' . $e->getMessage());
            return null;
        }
    }

    public function getStudentData($id)
    {
        try {
            $this->db->select('
                students.id as lib_member_id,
                "student" as member_type,
                students.*,
                sessions.session as session_year
            ');
            $this->db->from('students');
            $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
            $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
            $this->db->where('students.id', $id);
            $this->db->where('students.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->admission_no ? $result->admission_no : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getStudentData error: ' . $e->getMessage());
            return null;
        }
    }

    public function surrender($id)
    {
        // Simplified version for now - just return true
        // This will be enhanced later once the basic system is working
        return true;
    }

}
