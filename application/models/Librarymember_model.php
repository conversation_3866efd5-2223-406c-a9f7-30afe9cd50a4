<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends MY_Model
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * This funtion takes id as a parameter and will fetch the record.
     * If id is not provided, then it will fetch all the records form the table.
     * @param int $id
     * @return mixed
     */
    public function get()
    {
        // Get all active students as library members with auto-generated library card numbers
        $students_query = "SELECT
            students.id as lib_member_id,
            CONCAT('LIB-', students.admission_no) as library_card_no,
            'student' as member_type,
            students.admission_no,
            students.firstname,
            students.lastname,
            students.guardian_phone,
            null as teacher_name,
            null as teacher_email,
            null as teacher_sex,
            null as teacher_phone,
            students.middlename,
            null as staff_id,
            students.id as stu_id,
            null as emp_id
        FROM students
        WHERE students.is_active = 'yes'";

        // Get all active staff as library members with auto-generated library card numbers
        $staff_query = "SELECT
            staff.id as lib_member_id,
            CONCAT('LIB-', staff.employee_id) as library_card_no,
            'staff' as member_type,
            staff.employee_id as admission_no,
            null as firstname,
            null as lastname,
            null as guardian_phone,
            CONCAT(staff.name, ' ', staff.surname) as teacher_name,
            staff.email as teacher_email,
            staff.sex as teacher_sex,
            staff.contact_no as teacher_phone,
            null as middlename,
            staff.id as staff_id,
            null as stu_id,
            staff.employee_id as emp_id
        FROM staff
        WHERE staff.is_active = 'yes'";

        // Combine both queries
        $query = $students_query . " UNION " . $staff_query . " ORDER BY member_type, admission_no";
        $result = $this->db->query($query);
        return $result->result_array();
    }

    public function checkIsMember($member_type, $id)
    {
        // Get admission number based on member type and ID
        $admission_no = null;

        if ($member_type == 'student') {
            $student = $this->db->select('admission_no')
                               ->from('students')
                               ->where('id', $id)
                               ->where('is_active', 'yes')
                               ->get()->row();
            if ($student) {
                $admission_no = $student->admission_no;
            }
        } elseif ($member_type == 'staff' || $member_type == 'teacher') {
            $staff = $this->db->select('employee_id')
                             ->from('staff')
                             ->where('id', $id)
                             ->where('is_active', 'yes')
                             ->get()->row();
            if ($staff) {
                $admission_no = $staff->employee_id;
            }
        }

        if ($admission_no) {
            // Get issued books using admission number
            $book_lists = $this->bookissue_model->book_issuedByAdmissionNo($admission_no);
            return $book_lists;
        } else {
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }

        // First try to find as student
        $student = $this->getStudentData($id);
        if ($student) {
            return $student;
        }

        // Then try to find as staff
        $staff = $this->getTeacherData($id);
        if ($staff) {
            return $staff;
        }

        return null;
    }

    public function getByAdmissionNo($admission_no = null)
    {
        if ($admission_no == null) {
            return null;
        }

        // First try to find as student
        $student = $this->db->select('
            students.id as lib_member_id,
            CONCAT("LIB-", students.admission_no) as library_card_no,
            "student" as member_type,
            students.*
        ')->from('students')
        ->where('students.admission_no', $admission_no)
        ->where('students.is_active', 'yes')
        ->get()->row();

        if ($student) {
            return $student;
        }

        // Then try to find as staff
        $staff = $this->db->select('
            staff.id as lib_member_id,
            CONCAT("LIB-", staff.employee_id) as library_card_no,
            "staff" as member_type,
            staff.*
        ')->from('staff')
        ->where('staff.employee_id', $admission_no)
        ->where('staff.is_active', 'yes')
        ->get()->row();

        return $staff;
    }

    public function getTeacherData($id)
    {
        $this->db->select('
            staff.id as lib_member_id,
            CONCAT("LIB-", staff.employee_id) as library_card_no,
            "staff" as member_type,
            staff.*
        ');
        $this->db->from('staff');
        $this->db->where('staff.id', $id);
        $this->db->where('staff.is_active', 'yes');
        $query  = $this->db->get();
        $result = $query->row();
        return $result;
    }

    public function getStudentData($id)
    {
        $this->db->select('
            students.id as lib_member_id,
            CONCAT("LIB-", students.admission_no) as library_card_no,
            "student" as member_type,
            students.*,
            sessions.session as session_year
        ');
        $this->db->from('students');
        $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
        $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
        $this->db->where('students.id', $id);
        $this->db->where('students.is_active', 'yes');
        $query  = $this->db->get();
        $result = $query->row();
        return $result;
    }

    public function surrender($id)
    {
        // In the new system, we don't delete library members since they are auto-generated
        // We only need to handle book returns and mark student/staff as inactive if needed

        $this->db->trans_start(); # Starting Transaction
        $this->db->trans_strict(false); # See Note 01. If you wish can remove as well
        //=======================Code Start===========================

        // Get member details first
        $member = $this->getByMemberID($id);
        if (!$member) {
            return false;
        }

        // Return all issued books for this member
        $admission_no = ($member->member_type == 'student') ? $member->admission_no : $member->employee_id;

        $this->db->where('member_admission_no', $admission_no);
        $this->db->where('is_returned', 0);
        $this->db->update('book_issues', array('is_returned' => 1, 'return_date' => date('Y-m-d')));

        $message   = "Library surrender for member: " . $admission_no;
        $action    = "Library Surrender";
        $record_id = $id;
        $this->log($message, $record_id, $action);

        //======================Code End==============================
        $this->db->trans_complete(); # Completing transaction
        /* Optional */
        if ($this->db->trans_status() === false) {
            # Something went wrong.
            $this->db->trans_rollback();
            return false;
        } else {
            return true;
        }
    }

}
