<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends MY_Model
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * This funtion takes id as a parameter and will fetch the record.
     * If id is not provided, then it will fetch all the records form the table.
     * @param int $id
     * @return mixed
     */
    public function get()
    {
        try {
            $result = array();

            // Get students
            $students_query = $this->db->select('
                students.id as lib_member_id,
                students.id as stu_id,
                students.admission_no,
                students.firstname,
                students.lastname,
                students.middlename,
                students.guardian_phone,
                "student" as member_type,
                NULL as teacher_name,
                NULL as teacher_email,
                NULL as teacher_sex,
                NULL as teacher_phone,
                NULL as staff_id,
                NULL as emp_id
            ')->from('students')
            ->where('students.is_active', 'yes')
            ->get();

            if ($students_query) {
                $students = $students_query->result_array();
                foreach ($students as &$student) {
                    $student['library_card_no'] = 'LIB-' . ($student['admission_no'] ? $student['admission_no'] : $student['lib_member_id']);
                }
                $result = array_merge($result, $students);
            }

            // Get staff
            $staff_query = $this->db->select('
                staff.id as lib_member_id,
                staff.id as staff_id,
                staff.employee_id as admission_no,
                staff.employee_id as emp_id,
                staff.name,
                staff.surname,
                staff.email as teacher_email,
                staff.sex as teacher_sex,
                staff.contact_no as teacher_phone,
                "staff" as member_type,
                NULL as firstname,
                NULL as lastname,
                NULL as middlename,
                NULL as guardian_phone,
                NULL as stu_id
            ')->from('staff')
            ->where('staff.is_active', 'yes')
            ->get();

            if ($staff_query) {
                $staff = $staff_query->result_array();
                foreach ($staff as &$staff_member) {
                    $staff_member['library_card_no'] = 'LIB-' . ($staff_member['admission_no'] ? $staff_member['admission_no'] : $staff_member['lib_member_id']);
                    $staff_member['teacher_name'] = trim($staff_member['name'] . ' ' . $staff_member['surname']);
                }
                $result = array_merge($result, $staff);
            }

            return $result;

        } catch (Exception $e) {
            // Log error and return empty array to prevent 500 error
            log_message('error', 'Library member model error: ' . $e->getMessage());
            return array();
        }
    }



    public function checkIsMember($member_type, $id)
    {
        try {
            // For now, just return empty array to get the page working
            // This will be enhanced later
            return array();
        } catch (Exception $e) {
            log_message('error', 'checkIsMember error: ' . $e->getMessage());
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }

        try {
            // First try to find as student
            $student = $this->getStudentData($id);
            if ($student) {
                return $student;
            }

            // Then try to find as staff
            $staff = $this->getTeacherData($id);
            if ($staff) {
                return $staff;
            }

            return null;
        } catch (Exception $e) {
            log_message('error', 'getByMemberID error: ' . $e->getMessage());
            return null;
        }
    }

    public function getTeacherData($id)
    {
        try {
            $this->db->select('
                staff.id as lib_member_id,
                "staff" as member_type,
                staff.*
            ');
            $this->db->from('staff');
            $this->db->where('staff.id', $id);
            $this->db->where('staff.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->employee_id ? $result->employee_id : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getTeacherData error: ' . $e->getMessage());
            return null;
        }
    }

    public function getStudentData($id)
    {
        try {
            $this->db->select('
                students.id as lib_member_id,
                "student" as member_type,
                students.*,
                sessions.session as session_year
            ');
            $this->db->from('students');
            $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
            $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
            $this->db->where('students.id', $id);
            $this->db->where('students.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();

            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->admission_no ? $result->admission_no : $result->id);
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'getStudentData error: ' . $e->getMessage());
            return null;
        }
    }

    public function surrender($id)
    {
        // Simplified version for now - just return true
        // This will be enhanced later once the basic system is working
        return true;
    }

}
