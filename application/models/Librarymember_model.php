<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends MY_Model
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * This funtion takes id as a parameter and will fetch the record.
     * If id is not provided, then it will fetch all the records form the table.
     * @param int $id
     * @return mixed
     */
    public function get()
    {
        try {
            // Check if we should use the new system or fall back to old system
            $use_new_system = $this->checkNewSystemAvailable();

            if ($use_new_system) {
                return $this->getUnifiedMembers();
            } else {
                // Fallback to old system if new columns don't exist
                return $this->getOldSystemMembers();
            }

        } catch (Exception $e) {
            // Log error and return empty array to prevent 500 error
            log_message('error', 'Library member model error: ' . $e->getMessage());
            return array();
        }
    }

    private function checkNewSystemAvailable()
    {
        try {
            // Check if new columns exist in book_issues table
            $fields = $this->db->list_fields('book_issues');
            return in_array('member_admission_no', $fields) && in_array('member_type', $fields);
        } catch (Exception $e) {
            return false;
        }
    }

    private function getUnifiedMembers()
    {
        // Get all active students as library members with auto-generated library card numbers
        $students = $this->db->select('
            students.id as lib_member_id,
            CONCAT("LIB-", students.admission_no) as library_card_no,
            "student" as member_type,
            students.admission_no,
            students.firstname,
            students.lastname,
            students.guardian_phone,
            NULL as teacher_name,
            NULL as teacher_email,
            NULL as teacher_sex,
            NULL as teacher_phone,
            students.middlename,
            NULL as staff_id,
            students.id as stu_id,
            NULL as emp_id
        ')->from('students')
        ->where('students.is_active', 'yes')
        ->get()->result_array();

        // Get all active staff as library members with auto-generated library card numbers
        $staff = $this->db->select('
            staff.id as lib_member_id,
            CONCAT("LIB-", staff.employee_id) as library_card_no,
            "staff" as member_type,
            staff.employee_id as admission_no,
            NULL as firstname,
            NULL as lastname,
            NULL as guardian_phone,
            CONCAT(staff.name, " ", staff.surname) as teacher_name,
            staff.email as teacher_email,
            staff.sex as teacher_sex,
            staff.contact_no as teacher_phone,
            NULL as middlename,
            staff.id as staff_id,
            NULL as stu_id,
            staff.employee_id as emp_id
        ')->from('staff')
        ->where('staff.is_active', 'yes')
        ->get()->result_array();

        // Combine students and staff
        $result = array_merge($students, $staff);

        // Sort by member type and admission number
        usort($result, function($a, $b) {
            if ($a['member_type'] == $b['member_type']) {
                return strcmp($a['admission_no'], $b['admission_no']);
            }
            return strcmp($a['member_type'], $b['member_type']);
        });

        return $result;
    }

    private function getOldSystemMembers()
    {
        // Fallback to original query structure for backward compatibility
        $query = "SELECT libarary_members.id as `lib_member_id`,libarary_members.library_card_no,libarary_members.member_type,students.admission_no,students.firstname,students.lastname,students.guardian_phone,null as `teacher_name`,null as `teacher_email`,null as `teacher_sex`,null as `teacher_phone`,students.middlename, null as staff_id,students.id as stu_id,null as emp_id FROM `libarary_members` INNER JOIN students on libarary_members.member_id= students.id WHERE libarary_members.member_type='student' and students.is_active = 'yes' UNION SELECT libarary_members.id as `lib_member_id`,libarary_members.library_card_no,libarary_members.member_type,null,null,null,null,CONCAT_WS(' ',staff.name,staff.surname) as name,staff.email,null,staff.contact_no,null, staff.id as staff_id,null as stu_id,staff.employee_id as emp_id FROM `libarary_members` INNER JOIN staff on libarary_members.member_id= staff.id WHERE libarary_members.member_type='teacher' ";
        $result = $this->db->query($query);
        return $result->result_array();
    }

    public function checkIsMember($member_type, $id)
    {
        try {
            // Get admission number based on member type and ID
            $admission_no = null;

            if ($member_type == 'student') {
                $student = $this->db->select('admission_no')
                                   ->from('students')
                                   ->where('id', $id)
                                   ->where('is_active', 'yes')
                                   ->get()->row();
                if ($student) {
                    $admission_no = $student->admission_no;
                }
            } elseif ($member_type == 'staff' || $member_type == 'teacher') {
                $staff = $this->db->select('employee_id')
                                 ->from('staff')
                                 ->where('id', $id)
                                 ->where('is_active', 'yes')
                                 ->get()->row();
                if ($staff) {
                    $admission_no = $staff->employee_id;
                }
            }

            if ($admission_no) {
                // Check if the new method exists, if not use old method
                if (method_exists($this->bookissue_model, 'book_issuedByAdmissionNo')) {
                    $book_lists = $this->bookissue_model->book_issuedByAdmissionNo($admission_no);
                } else {
                    // Fallback to old method
                    $book_lists = $this->bookissue_model->book_issuedByMemberID($id);
                }
                return $book_lists;
            } else {
                return false;
            }
        } catch (Exception $e) {
            log_message('error', 'checkIsMember error: ' . $e->getMessage());
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }

        // First try to find as student
        $student = $this->getStudentData($id);
        if ($student) {
            return $student;
        }

        // Then try to find as staff
        $staff = $this->getTeacherData($id);
        if ($staff) {
            return $staff;
        }

        return null;
    }

    public function getByAdmissionNo($admission_no = null)
    {
        if ($admission_no == null) {
            return null;
        }

        // First try to find as student
        $student = $this->db->select('
            students.id as lib_member_id,
            CONCAT("LIB-", students.admission_no) as library_card_no,
            "student" as member_type,
            students.*
        ')->from('students')
        ->where('students.admission_no', $admission_no)
        ->where('students.is_active', 'yes')
        ->get()->row();

        if ($student) {
            return $student;
        }

        // Then try to find as staff
        $staff = $this->db->select('
            staff.id as lib_member_id,
            CONCAT("LIB-", staff.employee_id) as library_card_no,
            "staff" as member_type,
            staff.*
        ')->from('staff')
        ->where('staff.employee_id', $admission_no)
        ->where('staff.is_active', 'yes')
        ->get()->row();

        return $staff;
    }

    public function getTeacherData($id)
    {
        $this->db->select('
            staff.id as lib_member_id,
            CONCAT("LIB-", staff.employee_id) as library_card_no,
            "staff" as member_type,
            staff.*
        ');
        $this->db->from('staff');
        $this->db->where('staff.id', $id);
        $this->db->where('staff.is_active', 'yes');
        $query  = $this->db->get();
        $result = $query->row();
        return $result;
    }

    public function getStudentData($id)
    {
        $this->db->select('
            students.id as lib_member_id,
            CONCAT("LIB-", students.admission_no) as library_card_no,
            "student" as member_type,
            students.*,
            sessions.session as session_year
        ');
        $this->db->from('students');
        $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
        $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
        $this->db->where('students.id', $id);
        $this->db->where('students.is_active', 'yes');
        $query  = $this->db->get();
        $result = $query->row();
        return $result;
    }

    public function surrender($id)
    {
        // In the new system, we don't delete library members since they are auto-generated
        // We only need to handle book returns and mark student/staff as inactive if needed

        $this->db->trans_start(); # Starting Transaction
        $this->db->trans_strict(false); # See Note 01. If you wish can remove as well
        //=======================Code Start===========================

        // Get member details first
        $member = $this->getByMemberID($id);
        if (!$member) {
            return false;
        }

        // Return all issued books for this member
        $admission_no = ($member->member_type == 'student') ? $member->admission_no : $member->employee_id;

        $this->db->where('member_admission_no', $admission_no);
        $this->db->where('is_returned', 0);
        $this->db->update('book_issues', array('is_returned' => 1, 'return_date' => date('Y-m-d')));

        $message   = "Library surrender for member: " . $admission_no;
        $action    = "Library Surrender";
        $record_id = $id;
        $this->log($message, $record_id, $action);

        //======================Code End==============================
        $this->db->trans_complete(); # Completing transaction
        /* Optional */
        if ($this->db->trans_status() === false) {
            # Something went wrong.
            $this->db->trans_rollback();
            return false;
        } else {
            return true;
        }
    }

}
