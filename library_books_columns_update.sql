-- SQL Query to add new columns to books table
-- Run this in phpMyAdmin SQL tab

ALTER TABLE `books` 
ADD COLUMN `book_type` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Type of book (Fiction, Non-Fiction, Reference, etc.)' AFTER `description`,
ADD COLUMN `book_category` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Category of book (Science, Literature, History, etc.)' AFTER `book_type`,
ADD COLUMN `book_collection` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Collection name (Main Collection, Special Collection, etc.)' AFTER `book_category`,
ADD COLUMN `book_language` VARCHAR(50) NULL DEFAULT NULL COMMENT 'Language of the book' AFTER `book_collection`,
ADD COLUMN `shelving_location` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Physical location/shelf details' AFTER `book_language`,
ADD COLUMN `source_of_classification` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Classification system used (Dewey Decimal, Library of Congress, etc.)' AFTER `shelving_location`,
ADD COLUMN `lost_status` ENUM('Available', 'Lost', 'Damaged', 'Missing', 'Under Repair') NOT NULL DEFAULT 'Available' COMMENT 'Current status of the book' AFTER `source_of_classification`;

-- Optional: Add indexes for better performance on search fields
ALTER TABLE `books` 
ADD INDEX `idx_book_type` (`book_type`),
ADD INDEX `idx_book_category` (`book_category`),
ADD INDEX `idx_book_language` (`book_language`),
ADD INDEX `idx_lost_status` (`lost_status`);
