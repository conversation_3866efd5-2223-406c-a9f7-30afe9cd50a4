<?php
/**
 * Quick test to check if the member page loads
 * Access this via: https://erp.nbxc.edu.in/test_member_page.php
 * Remove after testing
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Member Page Test</h2>";

try {
    // Test database connection first
    $host = 'localhost';
    $dbname = 'erp_nbxcdb';
    $username = 'root'; // Adjust as needed
    $password = ''; // Adjust as needed
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    echo "✅ Database connection successful<br>";
    
    // Test students table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM students WHERE is_active = 'yes'");
    $student_count = $stmt->fetch()['count'];
    echo "✅ Active students: $student_count<br>";
    
    // Test staff table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM staff WHERE is_active = 'yes'");
    $staff_count = $stmt->fetch()['count'];
    echo "✅ Active staff: $staff_count<br>";
    
    // Test book_issues table structure
    $stmt = $pdo->query("SHOW COLUMNS FROM book_issues");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('member_admission_no', $columns)) {
        echo "✅ member_admission_no column exists<br>";
    } else {
        echo "❌ member_admission_no column missing<br>";
    }
    
    if (in_array('member_type', $columns)) {
        echo "✅ member_type column exists<br>";
    } else {
        echo "❌ member_type column missing<br>";
    }
    
    // Test sample query
    $stmt = $pdo->query("
        SELECT 
            students.id as lib_member_id,
            students.admission_no,
            students.firstname,
            students.lastname,
            'student' as member_type
        FROM students 
        WHERE students.is_active = 'yes' 
        LIMIT 3
    ");
    $sample_students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Sample Students:</h3>";
    foreach ($sample_students as $student) {
        $library_card = 'LIB-' . ($student['admission_no'] ? $student['admission_no'] : $student['lib_member_id']);
        echo "- {$student['firstname']} {$student['lastname']} (Card: $library_card)<br>";
    }
    
    // Test sample staff query
    $stmt = $pdo->query("
        SELECT 
            staff.id as lib_member_id,
            staff.employee_id,
            staff.name,
            staff.surname,
            'staff' as member_type
        FROM staff 
        WHERE staff.is_active = 'yes' 
        LIMIT 3
    ");
    $sample_staff = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Sample Staff:</h3>";
    foreach ($sample_staff as $staff) {
        $library_card = 'LIB-' . ($staff['employee_id'] ? $staff['employee_id'] : $staff['lib_member_id']);
        echo "- {$staff['name']} {$staff['surname']} (Card: $library_card)<br>";
    }
    
    echo "<h3>✅ All tests passed!</h3>";
    echo "<p>The member page should now work. Try accessing: <a href='/admin/member'>/admin/member</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<p><strong>Remove this test file after debugging!</strong></p>";
?>
