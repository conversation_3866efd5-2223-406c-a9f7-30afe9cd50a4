<?php
/**
 * Debug script to check library members functionality
 * Place this file in your web root and access it via browser
 * Remove after debugging
 */

// Include CodeIgniter bootstrap
require_once('index.php');

echo "<h2>Library Members Debug</h2>";

// Get CI instance
$CI =& get_instance();

echo "<h3>1. Database Connection Test</h3>";
try {
    $db_test = $CI->db->query("SELECT 1 as test")->row();
    echo "✅ Database connection: OK<br>";
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "<br>";
}

echo "<h3>2. Students Table Test</h3>";
try {
    $students = $CI->db->select('id, admission_no, firstname, lastname, is_active')
                      ->from('students')
                      ->where('is_active', 'yes')
                      ->limit(5)
                      ->get()->result_array();
    echo "✅ Students found: " . count($students) . "<br>";
    if (!empty($students)) {
        echo "<pre>";
        print_r($students[0]);
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "❌ Students table error: " . $e->getMessage() . "<br>";
}

echo "<h3>3. Staff Table Test</h3>";
try {
    $staff = $CI->db->select('id, employee_id, name, surname, is_active')
                   ->from('staff')
                   ->where('is_active', 'yes')
                   ->limit(5)
                   ->get()->result_array();
    echo "✅ Staff found: " . count($staff) . "<br>";
    if (!empty($staff)) {
        echo "<pre>";
        print_r($staff[0]);
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "❌ Staff table error: " . $e->getMessage() . "<br>";
}

echo "<h3>4. Book Issues Table Structure</h3>";
try {
    $fields = $CI->db->list_fields('book_issues');
    echo "✅ Book issues table fields:<br>";
    echo "<ul>";
    foreach ($fields as $field) {
        echo "<li>$field</li>";
    }
    echo "</ul>";
    
    // Check if new columns exist
    if (in_array('member_admission_no', $fields)) {
        echo "✅ member_admission_no column exists<br>";
    } else {
        echo "❌ member_admission_no column missing - run migration script<br>";
    }
    
    if (in_array('member_type', $fields)) {
        echo "✅ member_type column exists<br>";
    } else {
        echo "❌ member_type column missing - run migration script<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Book issues table error: " . $e->getMessage() . "<br>";
}

echo "<h3>5. Library Member Model Test</h3>";
try {
    $CI->load->model('librarymember_model');
    $members = $CI->librarymember_model->get();
    echo "✅ Library members found: " . count($members) . "<br>";
    if (!empty($members)) {
        echo "Sample member:<br>";
        echo "<pre>";
        print_r($members[0]);
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "❌ Library member model error: " . $e->getMessage() . "<br>";
    echo "Error details: " . $CI->db->last_query() . "<br>";
}

echo "<h3>6. Library Card Number Generation Test</h3>";
try {
    // Test with first student
    $student = $CI->db->select('id, admission_no, firstname, lastname')
                     ->from('students')
                     ->where('is_active', 'yes')
                     ->limit(1)
                     ->get()->row();
    
    if ($student) {
        $library_card = 'LIB-' . $student->admission_no;
        echo "✅ Student: {$student->firstname} {$student->lastname}<br>";
        echo "✅ Admission No: {$student->admission_no}<br>";
        echo "✅ Library Card: {$library_card}<br>";
    } else {
        echo "❌ No active students found<br>";
    }
    
    // Test with first staff
    $staff = $CI->db->select('id, employee_id, name, surname')
                   ->from('staff')
                   ->where('is_active', 'yes')
                   ->limit(1)
                   ->get()->row();
    
    if ($staff) {
        $library_card = 'LIB-' . $staff->employee_id;
        echo "✅ Staff: {$staff->name} {$staff->surname}<br>";
        echo "✅ Employee ID: {$staff->employee_id}<br>";
        echo "✅ Library Card: {$library_card}<br>";
    } else {
        echo "❌ No active staff found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Library card generation error: " . $e->getMessage() . "<br>";
}

echo "<h3>7. Error Log Check</h3>";
$log_file = APPPATH . 'logs/log-' . date('Y-m-d') . '.php';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $error_lines = array_filter(explode("\n", $log_content), function($line) {
        return strpos($line, 'ERROR') !== false;
    });
    
    if (!empty($error_lines)) {
        echo "❌ Recent errors found:<br>";
        echo "<pre>";
        foreach (array_slice($error_lines, -5) as $line) {
            echo htmlspecialchars($line) . "\n";
        }
        echo "</pre>";
    } else {
        echo "✅ No recent errors in log<br>";
    }
} else {
    echo "ℹ️ No log file found for today<br>";
}

echo "<h3>8. Recommendations</h3>";
echo "<ul>";
echo "<li>If member_admission_no or member_type columns are missing, run the database migration script</li>";
echo "<li>If students/staff tables are empty, add some test data</li>";
echo "<li>Check CodeIgniter error logs for detailed error messages</li>";
echo "<li>Ensure database user has proper permissions</li>";
echo "</ul>";

echo "<p><strong>Remove this debug file after troubleshooting!</strong></p>";
?>
