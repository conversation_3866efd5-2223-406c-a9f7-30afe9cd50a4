<?php
/**
 * Quick fix to show all members regardless of is_active status
 * This will help identify the issue
 * Access via: https://erp.nbxc.edu.in/quick_fix_members.php
 * Remove after debugging
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Quick Fix - Show All Members</h2>";

// Include CodeIgniter
require_once('index.php');
$CI =& get_instance();

echo "<h3>All Students (regardless of status):</h3>";
try {
    $students = $CI->db->query("
        SELECT 
            id,
            admission_no,
            firstname,
            lastname,
            is_active,
            CONCAT('LIB-', COALESCE(admission_no, id)) as library_card_no
        FROM students 
        ORDER BY id 
        LIMIT 20
    ")->result_array();
    
    if (!empty($students)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Admission No</th><th>Name</th><th>Library Card</th><th>Status</th></tr>";
        foreach ($students as $student) {
            echo "<tr>";
            echo "<td>{$student['id']}</td>";
            echo "<td>{$student['admission_no']}</td>";
            echo "<td>{$student['firstname']} {$student['lastname']}</td>";
            echo "<td>{$student['library_card_no']}</td>";
            echo "<td>{$student['is_active']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No students found in database<br>";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}

echo "<h3>All Staff (regardless of status):</h3>";
try {
    $staff = $CI->db->query("
        SELECT 
            id,
            employee_id,
            name,
            surname,
            is_active,
            CONCAT('LIB-', COALESCE(employee_id, id)) as library_card_no
        FROM staff 
        ORDER BY id 
        LIMIT 20
    ")->result_array();
    
    if (!empty($staff)) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Employee ID</th><th>Name</th><th>Library Card</th><th>Status</th></tr>";
        foreach ($staff as $staff_member) {
            echo "<tr>";
            echo "<td>{$staff_member['id']}</td>";
            echo "<td>{$staff_member['employee_id']}</td>";
            echo "<td>{$staff_member['name']} {$staff_member['surname']}</td>";
            echo "<td>{$staff_member['library_card_no']}</td>";
            echo "<td>{$staff_member['is_active']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No staff found in database<br>";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}

echo "<h3>Recommendations:</h3>";
echo "<ul>";
echo "<li>If you see users above, the issue is with the is_active filter</li>";
echo "<li>Check what values are in the is_active column</li>";
echo "<li>Common values: 'yes', 'no', '1', '0', 'active', 'inactive'</li>";
echo "<li>Update the library model to use the correct is_active values</li>";
echo "</ul>";

echo "<p><strong>Remove this file after debugging!</strong></p>";
?>
