-- Database Migration for Library Direct Integration
-- This script modifies the book_issues table to support direct admission number integration
-- Run this script to update your database schema

-- Step 1: Add new columns to book_issues table
ALTER TABLE book_issues 
ADD COLUMN member_admission_no VARCHAR(50) AFTER member_id,
ADD COLUMN member_type ENUM('student', 'staff') AFTER member_admission_no;

-- Step 2: Add indexes for better performance
ALTER TABLE book_issues 
ADD INDEX idx_member_admission (member_admission_no),
ADD INDEX idx_member_type (member_type);

-- Step 3: Update existing data (if any) - since you mentioned tables are empty, this is precautionary
-- This will populate the new fields based on existing library members data
UPDATE book_issues bi
JOIN libarary_members lm ON bi.member_id = lm.id
JOIN students s ON lm.member_id = s.id AND lm.member_type = 'student'
SET bi.member_admission_no = s.admission_no, bi.member_type = 'student'
WHERE lm.member_type = 'student';

UPDATE book_issues bi
JOIN libarary_members lm ON bi.member_id = lm.id
JOIN staff st ON lm.member_id = st.id AND lm.member_type = 'teacher'
SET bi.member_admission_no = st.employee_id, bi.member_type = 'staff'
WHERE lm.member_type = 'teacher';

-- Step 4: Verify the migration
SELECT 
    COUNT(*) as total_issues,
    COUNT(member_admission_no) as issues_with_admission_no,
    COUNT(member_type) as issues_with_member_type
FROM book_issues;

-- Step 5: Show sample data to verify
SELECT 
    id,
    member_id,
    member_admission_no,
    member_type,
    book_id,
    issue_date,
    is_returned
FROM book_issues 
LIMIT 10;

-- Note: The old member_id column is kept for backward compatibility during transition
-- It can be removed later once all functionality is tested and working
