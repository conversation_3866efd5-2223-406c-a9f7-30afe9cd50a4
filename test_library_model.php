<?php
/**
 * Test the library member model directly
 * Access via: https://erp.nbxc.edu.in/test_library_model.php
 * Remove after debugging
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Test Library Member Model</h2>";

// Include CodeIgniter
require_once('index.php');
$CI =& get_instance();

echo "<h3>1. Basic Database Tests</h3>";

// Test students count
try {
    $student_count = $CI->db->query("SELECT COUNT(*) as count FROM students")->row()->count;
    echo "📊 Total students: $student_count<br>";
    
    if ($student_count > 0) {
        $sample_student = $CI->db->query("SELECT id, admission_no, firstname, lastname, is_active FROM students LIMIT 1")->row();
        echo "📋 Sample student: ID={$sample_student->id}, Name={$sample_student->firstname} {$sample_student->lastname}, Active={$sample_student->is_active}<br>";
    }
} catch (Exception $e) {
    echo "❌ Students error: " . $e->getMessage() . "<br>";
}

// Test staff count
try {
    $staff_count = $CI->db->query("SELECT COUNT(*) as count FROM staff")->row()->count;
    echo "📊 Total staff: $staff_count<br>";
    
    if ($staff_count > 0) {
        $sample_staff = $CI->db->query("SELECT id, employee_id, name, surname, is_active FROM staff LIMIT 1")->row();
        echo "📋 Sample staff: ID={$sample_staff->id}, Name={$sample_staff->name} {$sample_staff->surname}, Active={$sample_staff->is_active}<br>";
    }
} catch (Exception $e) {
    echo "❌ Staff error: " . $e->getMessage() . "<br>";
}

echo "<h3>2. Library Member Model Test</h3>";

try {
    $CI->load->model('librarymember_model');
    echo "✅ Model loaded successfully<br>";
    
    $members = $CI->librarymember_model->get();
    echo "📊 Members returned: " . count($members) . "<br>";
    
    if (count($members) > 0) {
        echo "✅ SUCCESS! Members found:<br>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Type</th><th>Name</th><th>Library Card</th><th>Admission/Employee ID</th><th>Active Status</th></tr>";
        
        foreach (array_slice($members, 0, 10) as $member) {
            $name = '';
            if ($member['member_type'] == 'student') {
                $name = $member['firstname'] . ' ' . $member['lastname'];
            } else {
                $name = $member['teacher_name'];
            }
            
            echo "<tr>";
            echo "<td>{$member['member_type']}</td>";
            echo "<td>$name</td>";
            echo "<td>{$member['library_card_no']}</td>";
            echo "<td>{$member['admission_no']}</td>";
            echo "<td>{$member['is_active']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (count($members) > 10) {
            echo "<p>... and " . (count($members) - 10) . " more members</p>";
        }
        
    } else {
        echo "❌ No members returned<br>";
        
        // Try direct queries
        echo "<h4>Direct Query Tests:</h4>";
        
        $direct_students = $CI->db->query("SELECT COUNT(*) as count FROM students")->row()->count;
        echo "Direct students query: $direct_students<br>";
        
        $direct_staff = $CI->db->query("SELECT COUNT(*) as count FROM staff")->row()->count;
        echo "Direct staff query: $direct_staff<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Model error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<h3>3. Next Steps</h3>";
echo "<ul>";
echo "<li>If members are found here, the model is working correctly</li>";
echo "<li>If no members found, check the is_active field values in your database</li>";
echo "<li>Try accessing the admin member page: <a href='/admin/member'>/admin/member</a></li>";
echo "</ul>";

echo "<p><strong>Remove this test file after debugging!</strong></p>";
?>
