<?php
/**
 * Test script to verify ISBN formatting for Google Books API
 */

// Test ISBNs from your database
$test_isbns = [
    '81-7808-475-9',
    '81-7808-476-7',
    '81-309-0698-8',
    '978-0-123456-78-9',
    '0-123456-78-9'
];

echo "<h2>ISBN Formatting Test</h2>\n";
echo "<table border='1' cellpadding='5'>\n";
echo "<tr><th>Original ISBN</th><th>Cleaned ISBN</th><th>Length</th><th>Valid</th><th>Google Books URL</th></tr>\n";

foreach ($test_isbns as $original_isbn) {
    // Clean ISBN (remove any hyphens, spaces, and non-alphanumeric characters except X)
    $cleaned_isbn = preg_replace('/[^0-9X]/i', '', $original_isbn);
    
    $length = strlen($cleaned_isbn);
    $is_valid = ($length == 10 || $length == 13) ? 'Yes' : 'No';
    $google_url = "https://www.googleapis.com/books/v1/volumes?q=isbn:" . $cleaned_isbn;
    
    echo "<tr>";
    echo "<td>$original_isbn</td>";
    echo "<td>$cleaned_isbn</td>";
    echo "<td>$length</td>";
    echo "<td>$is_valid</td>";
    echo "<td><a href='$google_url' target='_blank'>Test API Call</a></td>";
    echo "</tr>\n";
}

echo "</table>\n";

echo "<h3>Test API Call for First ISBN</h3>\n";

// Test actual API call for the first ISBN
$test_isbn = preg_replace('/[^0-9X]/i', '', $test_isbns[0]);
$api_url = "https://www.googleapis.com/books/v1/volumes?q=isbn:" . $test_isbn;

echo "<p><strong>API URL:</strong> <a href='$api_url' target='_blank'>$api_url</a></p>\n";

// Make actual API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'School ERP System');

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> $http_code</p>\n";
if ($error) {
    echo "<p><strong>cURL Error:</strong> $error</p>\n";
}

if ($response) {
    $data = json_decode($response, true);
    echo "<h4>API Response:</h4>\n";
    echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>\n";
    
    if (isset($data['items']) && !empty($data['items'])) {
        $book_info = $data['items'][0]['volumeInfo'];
        echo "<h4>Book Information Found:</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>Title:</strong> " . ($book_info['title'] ?? 'N/A') . "</li>\n";
        echo "<li><strong>Authors: <AUTHORS>
        echo "<li><strong>Publisher:</strong> " . ($book_info['publisher'] ?? 'N/A') . "</li>\n";
        echo "<li><strong>Published Date:</strong> " . ($book_info['publishedDate'] ?? 'N/A') . "</li>\n";
        echo "<li><strong>Thumbnail:</strong> " . (isset($book_info['imageLinks']['thumbnail']) ? $book_info['imageLinks']['thumbnail'] : 'N/A') . "</li>\n";
        echo "<li><strong>Description:</strong> " . (isset($book_info['description']) ? substr($book_info['description'], 0, 200) . '...' : 'N/A') . "</li>\n";
        echo "</ul>\n";
        
        if (isset($book_info['imageLinks']['thumbnail'])) {
            $thumbnail = str_replace('http:', 'https:', $book_info['imageLinks']['thumbnail']);
            echo "<h4>Book Cover:</h4>\n";
            echo "<img src='$thumbnail' style='max-width: 200px; border: 1px solid #ddd;' alt='Book Cover'>\n";
        }
    } else {
        echo "<p><strong>No book found for this ISBN</strong></p>\n";
    }
} else {
    echo "<p><strong>No response from API</strong></p>\n";
}

echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
