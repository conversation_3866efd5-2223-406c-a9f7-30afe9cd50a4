<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Librarymember_model extends CI_Model
{

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /**
     * SAFE VERSION - This will work even if migration hasn't been run
     * This function fetches all library members from students and staff tables directly
     * No separate library members table needed - all ERP users are automatically library members
     * @return mixed
     */
    public function get()
    {
        try {
            $result = array();
            
            // Get students
            $students_query = $this->db->select('
                students.id as lib_member_id,
                students.id as stu_id,
                students.admission_no,
                students.firstname,
                students.lastname,
                students.middlename,
                students.guardian_phone,
                "student" as member_type,
                NULL as teacher_name,
                NULL as teacher_email,
                NULL as teacher_sex,
                NULL as teacher_phone,
                NULL as staff_id,
                NULL as emp_id
            ')->from('students')
            ->where('students.is_active', 'yes')
            ->get();
            
            if ($students_query) {
                $students = $students_query->result_array();
                foreach ($students as &$student) {
                    $student['library_card_no'] = 'LIB-' . ($student['admission_no'] ? $student['admission_no'] : $student['lib_member_id']);
                }
                $result = array_merge($result, $students);
            }
            
            // Get staff
            $staff_query = $this->db->select('
                staff.id as lib_member_id,
                staff.id as staff_id,
                staff.employee_id as admission_no,
                staff.employee_id as emp_id,
                staff.name,
                staff.surname,
                staff.email as teacher_email,
                staff.sex as teacher_sex,
                staff.contact_no as teacher_phone,
                "staff" as member_type,
                NULL as firstname,
                NULL as lastname,
                NULL as middlename,
                NULL as guardian_phone,
                NULL as stu_id
            ')->from('staff')
            ->where('staff.is_active', 'yes')
            ->get();
            
            if ($staff_query) {
                $staff = $staff_query->result_array();
                foreach ($staff as &$staff_member) {
                    $staff_member['library_card_no'] = 'LIB-' . ($staff_member['admission_no'] ? $staff_member['admission_no'] : $staff_member['lib_member_id']);
                    $staff_member['teacher_name'] = trim($staff_member['name'] . ' ' . $staff_member['surname']);
                }
                $result = array_merge($result, $staff);
            }
            
            return $result;
            
        } catch (Exception $e) {
            // Log error and return empty array to prevent 500 error
            log_message('error', 'Library member model error: ' . $e->getMessage());
            return array();
        }
    }

    public function checkIsMember($member_type, $id)
    {
        try {
            // Get admission number based on member type and ID
            $admission_no = null;
            
            if ($member_type == 'student') {
                $student = $this->db->select('admission_no')
                                   ->from('students')
                                   ->where('id', $id)
                                   ->where('is_active', 'yes')
                                   ->get()->row();
                if ($student) {
                    $admission_no = $student->admission_no ? $student->admission_no : $id;
                }
            } elseif ($member_type == 'staff' || $member_type == 'teacher') {
                $staff = $this->db->select('employee_id')
                                 ->from('staff')
                                 ->where('id', $id)
                                 ->where('is_active', 'yes')
                                 ->get()->row();
                if ($staff) {
                    $admission_no = $staff->employee_id ? $staff->employee_id : $id;
                }
            }
            
            if ($admission_no) {
                // For now, return empty array since we're just getting the page to load
                return array();
            } else {
                return false;
            }
        } catch (Exception $e) {
            log_message('error', 'checkIsMember error: ' . $e->getMessage());
            return false;
        }
    }

    public function getByMemberID($id = null)
    {
        if ($id == null) {
            return null;
        }
        
        try {
            // First try to find as student
            $student = $this->db->select('
                students.id as lib_member_id,
                "student" as member_type,
                students.*
            ')->from('students')
            ->where('students.id', $id)
            ->where('students.is_active', 'yes')
            ->get()->row();
            
            if ($student) {
                $student->library_card_no = 'LIB-' . ($student->admission_no ? $student->admission_no : $student->id);
                return $student;
            }
            
            // Then try to find as staff
            $staff = $this->db->select('
                staff.id as lib_member_id,
                "staff" as member_type,
                staff.*
            ')->from('staff')
            ->where('staff.id', $id)
            ->where('staff.is_active', 'yes')
            ->get()->row();
            
            if ($staff) {
                $staff->library_card_no = 'LIB-' . ($staff->employee_id ? $staff->employee_id : $staff->id);
            }
            
            return $staff;
        } catch (Exception $e) {
            log_message('error', 'getByMemberID error: ' . $e->getMessage());
            return null;
        }
    }

    public function getTeacherData($id)
    {
        try {
            $this->db->select('
                staff.id as lib_member_id,
                "staff" as member_type,
                staff.*
            ');
            $this->db->from('staff');
            $this->db->where('staff.id', $id);
            $this->db->where('staff.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();
            
            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->employee_id ? $result->employee_id : $result->id);
            }
            
            return $result;
        } catch (Exception $e) {
            log_message('error', 'getTeacherData error: ' . $e->getMessage());
            return null;
        }
    }

    public function getStudentData($id)
    {
        try {
            $this->db->select('
                students.id as lib_member_id,
                "student" as member_type,
                students.*,
                sessions.session as session_year
            ');
            $this->db->from('students');
            $this->db->join('student_session', 'student_session.student_id = students.id', 'left');
            $this->db->join('sessions', 'sessions.id = student_session.session_id', 'left');
            $this->db->where('students.id', $id);
            $this->db->where('students.is_active', 'yes');
            $query  = $this->db->get();
            $result = $query->row();
            
            if ($result) {
                $result->library_card_no = 'LIB-' . ($result->admission_no ? $result->admission_no : $result->id);
            }
            
            return $result;
        } catch (Exception $e) {
            log_message('error', 'getStudentData error: ' . $e->getMessage());
            return null;
        }
    }

    public function surrender($id)
    {
        // Simplified version for now
        return true;
    }
}
