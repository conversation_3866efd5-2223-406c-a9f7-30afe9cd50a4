<?php
/**
 * Verification script to check if all required language strings are defined
 * Run this script to verify the language strings are properly added
 */

// Include the language file
include 'application/language/English/app_files/system_lang.php';

// Required language strings for the new book fields
$required_strings = [
    'book_type',
    'book_category', 
    'book_collection',
    'book_language',
    'shelving_location',
    'source_of_classification',
    'lost_status',
    'select'
];

echo "<h2>Language String Verification</h2>\n";
echo "<table border='1' cellpadding='5'>\n";
echo "<tr><th>Language Key</th><th>Status</th><th>Value</th></tr>\n";

$all_found = true;

foreach ($required_strings as $key) {
    $status = isset($lang[$key]) ? '✅ Found' : '❌ Missing';
    $value = isset($lang[$key]) ? $lang[$key] : 'NOT DEFINED';
    
    if (!isset($lang[$key])) {
        $all_found = false;
    }
    
    echo "<tr>";
    echo "<td>$key</td>";
    echo "<td>$status</td>";
    echo "<td>$value</td>";
    echo "</tr>\n";
}

echo "</table>\n";

if ($all_found) {
    echo "<h3 style='color: green;'>✅ All required language strings are properly defined!</h3>\n";
} else {
    echo "<h3 style='color: red;'>❌ Some language strings are missing. Please add them to the language file.</h3>\n";
}

// Also check some existing book-related strings for context
echo "<h3>Existing Book-Related Language Strings (for reference):</h3>\n";
echo "<ul>\n";
$book_strings = ['book_title', 'book_no', 'book_price', 'books', 'book_list'];
foreach ($book_strings as $key) {
    if (isset($lang[$key])) {
        echo "<li><strong>$key:</strong> {$lang[$key]}</li>\n";
    }
}
echo "</ul>\n";

echo "<p><em>Script completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
